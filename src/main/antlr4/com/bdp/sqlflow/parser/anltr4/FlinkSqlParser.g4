parser grammar FlinkSqlParser;

options { tokenVocab=FlinkSqlLexer; }

// Entry point
parse
    : statements* EOF
    ;

statements
    : singleStatement SEMICOLON?
    ;

singleStatement
    : statement
    ;

statement
    : query                                                                 #statementDefault
    | CREATE TABLE (IF NOT EXISTS)? qualifiedName 
        LEFT_PAREN columnDefinition (COMMA columnDefinition)* RIGHT_PAREN
        (COMMENT string)?
        (WITH LEFT_PAREN tableProperty (COMMA tableProperty)* RIGHT_PAREN)?  #createTable
    | CREATE (TEMPORARY)? VIEW (IF NOT EXISTS)? qualifiedName 
        (LEFT_PAREN columnAliases RIGHT_PAREN)?
        (COMMENT string)?
        AS query                                                            #createView
    | CREATE CATALOG qualifiedName 
        WITH LEFT_PAREN tableProperty (COMMA tableProperty)* RIGHT_PAREN    #createCatalog
    | INSERT (OVERWRITE)? (INTO)? (TABLE)? qualifiedName 
        (LEFT_PAREN columnAliases RIGHT_PAREN)? query                       #insertInto
    | DELETE FROM qualifiedName (WHERE booleanExpression)?                  #delete
    | DROP TABLE (IF EXISTS)? qualifiedName                                 #dropTable
    | DROP VIEW (IF EXISTS)? qualifiedName                                  #dropView
    | SHOW TABLES ((FROM | IN) qualifiedName)?                             #showTables
    | SHOW VIEWS ((FROM | IN) qualifiedName)?                              #showViews
    | EXPLAIN query                                                         #explain
    ;

query
    : with? queryNoWith
    ;

with
    : WITH namedQuery (COMMA namedQuery)*
    ;

namedQuery
    : name=identifier (LEFT_PAREN columnAliases RIGHT_PAREN)? AS LEFT_PAREN query RIGHT_PAREN
    ;

queryNoWith
    : queryTerm
      (ORDER BY sortItem (COMMA sortItem)*)?
      (LIMIT limit=INTEGER_VALUE)?
    ;

queryTerm
    : queryPrimary                                                          #queryTermDefault
    | left=queryTerm operator=INTERSECT setQuantifier? right=queryTerm      #setOperation
    | left=queryTerm operator=(UNION | EXCEPT) setQuantifier? right=queryTerm #setOperation
    ;

queryPrimary
    : querySpecification                                                    #queryPrimaryDefault
    | TABLE qualifiedName                                                   #table
    | VALUES expression (COMMA expression)*                                 #inlineTable
    | LEFT_PAREN queryNoWith RIGHT_PAREN                                    #subquery
    ;

querySpecification
    : SELECT setQuantifier? selectItem (COMMA selectItem)*
      (FROM relation (COMMA relation)*)?
      (WHERE where=booleanExpression)?
      (GROUP BY groupBy)?
      (HAVING having=booleanExpression)?
    ;

groupBy
    : setQuantifier? groupingElement (COMMA groupingElement)*
    ;

groupingElement
    : groupingSet                                                           #singleGroupingSet
    ;

groupingSet
    : LEFT_PAREN (expression (COMMA expression)*)? RIGHT_PAREN              #explicitGroupingSet
    | expression                                                            #simpleGroupingSet
    ;

selectItem
    : expression (AS? identifier)?                                          #selectSingle
    | qualifiedName DOT ASTERISK                                            #selectAll
    | ASTERISK                                                              #selectAll
    ;

relation
    : left=relation CROSS JOIN right=sampledRelation                        #crossJoin
    | left=relation joinType JOIN right=relation joinCriteria               #joinRelation
    | sampledRelation                                                       #relationDefault
    ;

joinType
    : INNER?
    | LEFT OUTER?
    | RIGHT OUTER?
    | FULL OUTER?
    ;

joinCriteria
    : ON booleanExpression
    | USING LEFT_PAREN identifier (COMMA identifier)* RIGHT_PAREN
    ;

sampledRelation
    : aliasedRelation
    ;

aliasedRelation
    : relationPrimary (AS? identifier columnAliases?)?
    ;

columnAliases
    : LEFT_PAREN identifier (COMMA identifier)* RIGHT_PAREN
    ;

relationPrimary
    : qualifiedName queryPeriod?                                            #tableName
    | LEFT_PAREN query RIGHT_PAREN                                          #subqueryRelation
    | UNNEST LEFT_PAREN expression (COMMA expression)* RIGHT_PAREN 
        (AS? identifier (LEFT_PAREN identifier (COMMA identifier)* RIGHT_PAREN)?)? #unnest
    | LATERAL TABLE LEFT_PAREN qualifiedName LEFT_PAREN 
        (expression (COMMA expression)*)? RIGHT_PAREN RIGHT_PAREN           #lateralView
    ;

expression
    : booleanExpression
    ;

booleanExpression
    : valueExpression predicate[$valueExpression.ctx]?                      #predicated
    | NOT booleanExpression                                                 #logicalNot
    | left=booleanExpression operator=AND right=booleanExpression           #logicalBinary
    | left=booleanExpression operator=OR right=booleanExpression            #logicalBinary
    ;

// Predicate rules
predicate[ParserRuleContext value]
    : comparisonOperator right=valueExpression                              #comparison
    | comparisonOperator comparisonQuantifier LEFT_PAREN query RIGHT_PAREN #quantifiedComparison
    | NOT? BETWEEN lower=valueExpression AND upper=valueExpression          #between
    | NOT? IN LEFT_PAREN expression (COMMA expression)* RIGHT_PAREN         #inList
    | NOT? IN LEFT_PAREN query RIGHT_PAREN                                  #inSubquery
    | NOT? LIKE pattern=valueExpression (ESCAPE escape=valueExpression)?    #like
    | IS NOT? NULL                                                          #nullPredicate
    | IS NOT? DISTINCT FROM right=valueExpression                           #distinctFrom
    ;

valueExpression
    : primaryExpression                                                     #valueExpressionDefault
    | valueExpression AT timeZoneSpecifier                                  #atTimeZone
    | operator=(MINUS | PLUS) valueExpression                               #arithmeticUnary
    | left=valueExpression operator=(ASTERISK | SLASH | PERCENT) right=valueExpression #arithmeticBinary
    | left=valueExpression operator=(PLUS | MINUS) right=valueExpression    #arithmeticBinary
    | left=valueExpression CONCAT right=valueExpression                     #concatenation
    ;

primaryExpression
    : NULL                                                                  #nullLiteral
    | interval                                                              #intervalLiteral
    | identifier string                                                     #typeConstructor
    | DOUBLE_VALUE                                                          #doubleLiteral
    | DECIMAL_VALUE                                                         #decimalLiteral
    | INTEGER_VALUE                                                         #integerLiteral
    | booleanValue                                                          #booleanLiteral
    | string                                                                #stringLiteral
    | BINARY_LITERAL                                                        #binaryLiteral
    | QUESTION_MARK                                                         #parameter
    | CURRENT_DATE                                                          #currentTime
    | CURRENT_TIME (LEFT_PAREN INTEGER_VALUE RIGHT_PAREN)?                  #currentTime
    | CURRENT_TIMESTAMP (LEFT_PAREN INTEGER_VALUE RIGHT_PAREN)?             #currentTime
    | qualifiedName LEFT_PAREN ASTERISK RIGHT_PAREN filter? over?           #functionCall
    | qualifiedName LEFT_PAREN (setQuantifier? expression (COMMA expression)*)? RIGHT_PAREN filter? over? #functionCall
    | identifier ARROW expression                                           #lambda
    | LEFT_PAREN query RIGHT_PAREN                                          #subqueryExpression
    | EXISTS LEFT_PAREN query RIGHT_PAREN                                   #exists
    | CASE valueExpression whenClause+ (ELSE elseExpression=expression)? END #simpleCase
    | CASE whenClause+ (ELSE elseExpression=expression)? END                #searchedCase
    | CAST LEFT_PAREN expression AS type RIGHT_PAREN                        #cast
    | ARRAY LEFT_BRACKET (expression (COMMA expression)*)? RIGHT_BRACKET    #arrayConstructor
    | value=primaryExpression LEFT_BRACKET index=valueExpression RIGHT_BRACKET #subscript
    | identifier                                                            #columnReference
    | base=primaryExpression DOT fieldName=identifier                       #dereference
    | LEFT_PAREN expression RIGHT_PAREN                                     #parenthesizedExpression
    | ROW LEFT_PAREN expression (COMMA expression)* RIGHT_PAREN             #rowConstructor
    ;

string
    : STRING                                                                #basicStringLiteral
    | UNICODE_STRING                                                        #unicodeStringLiteral
    ;

timeZoneSpecifier
    : TIME ZONE interval                                                    #timeZoneInterval
    | TIME ZONE string                                                      #timeZoneString
    ;

comparisonOperator
    : EQ | NEQ | LT | LTE | GT | GTE
    ;

comparisonQuantifier
    : ALL | SOME | ANY
    ;

booleanValue
    : TRUE | FALSE
    ;

interval
    : INTERVAL sign=(PLUS | MINUS)? string from=intervalField (TO to=intervalField)?
    ;

intervalField
    : YEAR | YEARS | QUARTER | MONTH | MONTHS | WEEK | WEEKS | DAY | DAYS | HOUR | HOURS | MINUTE | MINUTES | SECOND | SECONDS | MILLISECOND | MICROSECOND | NANOSECOND | EPOCH
    ;

type
    : ARRAY LT type GT                                                      #arrayType
    | MAP LT keyType=type COMMA valueType=type GT                           #mapType
    | ROW LT rowField (COMMA rowField)* GT                                  #rowType
    | baseType (LEFT_PAREN typeParameter (COMMA typeParameter)* RIGHT_PAREN)? #genericType
    ;

rowField
    : identifier type
    ;

typeParameter
    : INTEGER_VALUE | type
    ;

baseType
    : TIME_WITH_TIME_ZONE
    | TIMESTAMP_WITH_TIME_ZONE
    | DOUBLE
    | identifier
    ;

whenClause
    : WHEN condition=expression THEN result=expression
    ;

filter
    : FILTER LEFT_PAREN WHERE booleanExpression RIGHT_PAREN
    ;

over
    : OVER LEFT_PAREN
        (PARTITION BY partition+=expression (COMMA partition+=expression)*)?
        (ORDER BY sortItem (COMMA sortItem)*)?
      RIGHT_PAREN
    ;

sortItem
    : expression ordering=(ASC | DESC)? (NULLS nullOrdering=(FIRST | LAST))?
    ;

queryPeriod
    : FOR SYSTEM_TIME AS OF end=valueExpression
    ;

setQuantifier
    : DISTINCT
    | ALL
    ;

// Column definition for CREATE TABLE
columnDefinition
    : identifier type (METADATA (FROM string)?)? (COMMENT string)?
    ;

// Table properties for WITH clause
tableProperty
    : string EQ string
    ;

qualifiedName
    : identifier (DOT identifier)*
    ;

identifier
    : IDENTIFIER             #unquotedIdentifier
    | QUOTED_IDENTIFIER      #quotedIdentifier
    | BACKQUOTED_IDENTIFIER  #backQuotedIdentifier
    | DIGIT_IDENTIFIER       #digitIdentifier
    | nonReserved            #unquotedIdentifier
    ;

nonReserved
    : ANTI | AFTER | ALL | ANY | ARRAY | ASC | AT | ASYMMETRIC
    | BERNOULLI | BOTH
    | CLUSTER | COLUMN | COLUMNS | COMMENT | COMMIT | COUNT | CURRENT | COPARTITION | CUMULATE
    | DATA | DATE | DAY | DAYS | DEFINE | DEFINER | DESC | DESCRIPTOR | DISTRIBUTE | DOUBLE | DEFAULT
    | EMPTY | ERROR | EXCLUDING | EPOCH
    | FETCH | FILTER | FINAL | FIRST | FOLLOWING | FORMAT | FUNCTIONS
    | GROUPS | GLOBAL
    | HOUR | HOURS | HOP
    | IF | IGNORE | INCLUDING | INITIAL | INPUT | INTERVAL | INVOKER | IO | ISOLATION
    | JSON
    | KEEP
    | LAST | LATERAL | LEADING | LEVEL | LIMIT | LOCAL | LOGICAL
    | MAP | MATCH | MATCHED | MATCHES | MATCH_RECOGNIZE | MATERIALIZED | MEASURES | MERGE | MINUTE | MINUTES | MONTH | MONTHS | MILLISECOND | MICROSECOND
    | NEXT | NFC | NFD | NFKC | NFKD | NO | NONE | NULLIF | NULLS | NANOSECOND
    | OF | OFFSET | OMIT | ONE | ONLY | OPTION | ORDINALITY | OUTPUT | OVER | OVERFLOW | OVERWRITE
    | PARTITION | PARTITIONS | PAST | PATH | PATTERN | PER | PERMUTE | POSITION | PRECEDING | PRECISION | PROPERTIES | PRUNE
    | QUARTER
    | RANGE | READ | REFRESH | RENAME | REPEATABLE | REPLACE | RESET | RESPECT | RESTRICT | ROW | ROWS | RUNNING
    | SECOND | SECONDS | SECURITY | SEEK | SEMI | SET | SETS | SKIP_ | SIZE | STEP | SLIDE | SESSION | SORT
    | SHOW | SOME | SEPARATOR | START | STATS | SUBSET | SUBSTRING | SYSTEM | SYMMETRIC | SIMILAR
    | TABLES | TABLESAMPLE | TEXT | TIES | TIME | TIMECOL | TIMESTAMP | SYSTEM_TIME | TO | TRAILING | TRUNCATE | TRY_CAST | TUMBLE | TEMPORARY
    | UNBOUNDED | UNMATCHED | UPDATE | USE | USER | UNKNOWN
    | VERSION | VIEW
    | WINDOW | WITHIN | WITHOUT | WEEK | WEEKS
    | YEAR | YEARS
    | ZONE
    ;

