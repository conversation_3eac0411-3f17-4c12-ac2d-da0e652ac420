package io.github.melin.sqlflow.tree.expression;

import io.github.melin.sqlflow.AstVisitor;
import io.github.melin.sqlflow.tree.Node;
import io.github.melin.sqlflow.tree.NodeLocation;
import com.google.common.collect.ImmutableList;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static java.util.Objects.requireNonNull;

/**
 * huaixin 2021/12/21 11:27 AM
 */
public class QuantifiedComparisonExpression extends Expression {
    public enum Quantifier {
        ALL,
        ANY,
        SOME,
    }

    private final ComparisonExpression.Operator operator;
    private final Quantifier quantifier;
    private final Expression value;
    private final Expression subquery;

    public QuantifiedComparisonExpression(ComparisonExpression.Operator operator, Quantifier quantifier, Expression value, Expression subquery) {
        this(Optional.empty(), operator, quantifier, value, subquery);
    }

    public QuantifiedComparisonExpression(NodeLocation location, ComparisonExpression.Operator operator, Quantifier quantifier, Expression value, Expression subquery) {
        this(Optional.of(location), operator, quantifier, value, subquery);
    }

    private QuantifiedComparisonExpression(Optional<NodeLocation> location, ComparisonExpression.Operator operator, Quantifier quantifier, Expression value, Expression subquery) {
        super(location);
        this.operator = requireNonNull(operator, "operator is null");
        this.quantifier = requireNonNull(quantifier, "quantifier is null");
        this.value = requireNonNull(value, "value is null");
        this.subquery = requireNonNull(subquery, "subquery is null");
    }

    public ComparisonExpression.Operator getOperator() {
        return operator;
    }

    public Quantifier getQuantifier() {
        return quantifier;
    }

    public Expression getValue() {
        return value;
    }

    public Expression getSubquery() {
        return subquery;
    }

    @Override
    public <R, C> R accept(AstVisitor<R, C> visitor, C context) {
        return visitor.visitQuantifiedComparisonExpression(this, context);
    }

    @Override
    public List<? extends Node> getChildren() {
        return ImmutableList.of(value, subquery);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        QuantifiedComparisonExpression that = (QuantifiedComparisonExpression) o;
        return operator == that.operator &&
                quantifier == that.quantifier &&
                Objects.equals(value, that.value) &&
                Objects.equals(subquery, that.subquery);
    }

    @Override
    public int hashCode() {
        return Objects.hash(operator, quantifier, value, subquery);
    }

    @Override
    public boolean shallowEquals(Node other) {
        if (!sameClass(this, other)) {
            return false;
        }

        QuantifiedComparisonExpression otherNode = (QuantifiedComparisonExpression) other;
        return operator == otherNode.operator && quantifier == otherNode.quantifier;
    }
}
