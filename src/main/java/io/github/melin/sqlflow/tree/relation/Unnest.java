package io.github.melin.sqlflow.tree.relation;

import io.github.melin.sqlflow.AstVisitor;
import io.github.melin.sqlflow.tree.Node;
import io.github.melin.sqlflow.tree.NodeLocation;
import io.github.melin.sqlflow.tree.expression.Expression;
import com.google.common.collect.ImmutableList;
import io.github.melin.sqlflow.tree.expression.Identifier;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.google.common.base.MoreObjects.toStringHelper;
import static java.util.Objects.requireNonNull;

/**
 * huaixin 2021/12/27 7:58 PM
 */
public final class Unnest extends Relation {
    private final List<Expression> expressions;
    private final boolean withOrdinality;
    private final Optional<Identifier> tableAlias;
    private final Optional<List<Identifier>> columnNames;

    public Unnest(List<Expression> expressions, boolean withOrdinality) {
        this(Optional.empty(), expressions, withOrdinality, Optional.empty(), Optional.empty());
    }

    public Unnest(NodeLocation location, List<Expression> expressions, boolean withOrdinality) {
        this(Optional.of(location), expressions, withOrdinality, Optional.empty(), Optional.empty());
    }

    public Unnest(List<Expression> expressions, boolean withOrdinality, Optional<Identifier> tableAlias, Optional<List<Identifier>> columnNames) {
        this(Optional.empty(), expressions, withOrdinality, tableAlias, columnNames);
    }

    public Unnest(NodeLocation location, List<Expression> expressions, boolean withOrdinality, Optional<Identifier> tableAlias, Optional<List<Identifier>> columnNames) {
        this(Optional.of(location), expressions, withOrdinality, tableAlias, columnNames);
    }

    private Unnest(Optional<NodeLocation> location, List<Expression> expressions, boolean withOrdinality, Optional<Identifier> tableAlias, Optional<List<Identifier>> columnNames) {
        super(location);
        requireNonNull(expressions, "expressions is null");
        this.expressions = ImmutableList.copyOf(expressions);
        this.withOrdinality = withOrdinality;
        this.tableAlias = tableAlias;
        this.columnNames = columnNames.isPresent() ? columnNames.map(ImmutableList::copyOf) : Optional.empty();
    }

    public List<Expression> getExpressions() {
        return expressions;
    }

    public boolean isWithOrdinality() {
        return withOrdinality;
    }

    public Optional<Identifier> getTableAlias() {
        return tableAlias;
    }

    public Optional<List<Identifier>> getColumnNames() {
        return columnNames;
    }

    @Override
    public <R, C> R accept(AstVisitor<R, C> visitor, C context) {
        return visitor.visitUnnest(this, context);
    }

    @Override
    public List<? extends Node> getChildren() {
        ImmutableList.Builder<Node> nodes = ImmutableList.builder();
        nodes.addAll(expressions);
        tableAlias.ifPresent(nodes::add);
        columnNames.ifPresent(names -> nodes.addAll(names));
        return nodes.build();
    }

    @Override
    public String toString() {
        return toStringHelper(this)
                .add("expressions", expressions)
                .add("withOrdinality", withOrdinality)
                .add("tableAlias", tableAlias.orElse(null))
                .add("columnNames", columnNames.orElse(null))
                .toString();
    }

    @Override
    public int hashCode() {
        return Objects.hash(expressions, withOrdinality, tableAlias, columnNames);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }
        Unnest other = (Unnest) obj;
        return Objects.equals(expressions, other.expressions)
                && withOrdinality == other.withOrdinality
                && Objects.equals(tableAlias, other.tableAlias)
                && Objects.equals(columnNames, other.columnNames);
    }

    @Override
    public boolean shallowEquals(Node other) {
        if (!Node.sameClass(this, other)) {
            return false;
        }

        Unnest otherNode = (Unnest) other;
        return withOrdinality == otherNode.withOrdinality
                && Objects.equals(tableAlias, otherNode.tableAlias)
                && Objects.equals(columnNames, otherNode.columnNames);
    }
}
