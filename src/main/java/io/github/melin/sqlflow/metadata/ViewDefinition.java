package io.github.melin.sqlflow.metadata;

import com.google.common.collect.Lists;
import io.github.melin.superior.common.relational.table.ColumnRel;

import java.util.List;
import java.util.Optional;

import static com.google.common.base.MoreObjects.toStringHelper;
import static com.google.common.base.Preconditions.checkArgument;
import static java.util.Objects.requireNonNull;

/**
 * huaixin 2021/12/25 5:35 PM
 */
public class ViewDefinition {
    private final String originalSql;
    private final String catalog;
    private final String schema;
    private final String name;
    private final List<String> columns;
    private final String comment;


    public ViewDefinition(
            String originalSql,
            String catalog,
            String schema,
            String name,
            List<String> columns,
            String comment) {
        this.originalSql = requireNonNull(originalSql, "originalSql is null");
        this.catalog = catalog;
        this.schema = schema;
        this.name = requireNonNull(name, "view name is null");
        this.columns = Lists.newArrayList(requireNonNull(columns, "columns is null"));
        this.comment = comment;
        // checkArgument(schema == null || catalog.isPresent(), "catalog must be present if schema is present");
        checkArgument(!this.originalSql.isEmpty(), "originalSql is empty");
    }

    public String getOriginalSql() {
        return originalSql;
    }


    public String getCatalog() {
        return catalog;
    }

    public String getSchema() {
        return schema;
    }

    public String getName() {
        return name;
    }

    public List<String> getColumns() {
        return columns;
    }

    public String getComment() {
        return comment;
    }

    @Override
    public String toString() {
        if (catalog != null) {
            return catalog + '.' + schema + '.' + name;
        } else if (schema != null) {
            return schema + '.' + name;
        } else {
            return name;
        }
    }
}
