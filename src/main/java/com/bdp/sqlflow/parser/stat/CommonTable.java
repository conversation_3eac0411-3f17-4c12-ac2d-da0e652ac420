package com.bdp.sqlflow.parser.stat;

import com.bdp.sqlflow.parser.constant.CommonConstants;
import lombok.Data;

import java.util.HashSet;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Data
public class CommonTable extends CommonStatement{

    private CommonConstants.ObjectType type = CommonConstants.ObjectType.TABLE;

    private String catalog;
    private String schema;
    private String name;

    private String comment;
    private String originalSql;
    private String transform;

    private Map<String, String> withOptions;

    private Set<CommonColumn> columns = new HashSet<>();

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CommonTable that = (CommonTable) o;
        return Objects.equals(catalog, that.catalog) &&
                Objects.equals(schema, that.schema) &&
                Objects.equals(name, that.name);
    }

    @Override
    public int hashCode() {
        return Objects.hash(catalog, schema, name);
    }
}
