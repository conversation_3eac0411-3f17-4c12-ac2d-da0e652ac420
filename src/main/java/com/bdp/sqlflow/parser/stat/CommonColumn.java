package com.bdp.sqlflow.parser.stat;

import com.bdp.sqlflow.parser.constant.CommonConstants;
import lombok.Data;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
public class CommonColumn extends CommonStatement{

    private CommonConstants.ObjectType type = CommonConstants.ObjectType.COLUMN;

    private String catalog;
    private String schema;
    private String table;
    private String name;

    private String columnType;
    private String comment;
    private String originalSql;
    private String transform;


    // 根据catalog.schema.table.name 生成equlas和hashcode方法
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CommonColumn that = (CommonColumn) o;
        return Objects.equals(catalog, that.catalog) &&
                Objects.equals(schema, that.schema) &&
                Objects.equals(table, that.table) &&
                Objects.equals(name, that.name);
    }

    @Override
    public int hashCode() {
        return Objects.hash(catalog, schema, table, name);
    }
}
