package com.bdp.sqlflow.parser;

import com.bdp.sqlflow.parser.stat.CommonColumn;
import com.bdp.sqlflow.parser.stat.CommonTable;
import com.google.common.graph.MutableValueGraph;

import java.util.*;

/**
 * SQL解析工具类
 * 提供统一的SQL解析入口，支持多种数据库类型
 * 
 * <AUTHOR>
 */
public class SqlParserUtil {
    
    /**
     * 支持的SQL类型
     */
    public enum SqlType {
        FLINK_SQL,
        MYSQL,
        DORIS,
        SPARK_SQL
    }
    
    /**
     * 解析SQL并获取血缘关系
     * 
     * @param sql SQL语句
     * @param sqlType SQL类型
     * @return 血缘解析结果
     */
    public static LineageResult parseLineage(String sql, SqlType sqlType) {
        switch (sqlType) {
            case FLINK_SQL:
                return parseFlinkSql(sql);
            case MYSQL:
                throw new UnsupportedOperationException("MySQL parser not implemented yet");
            case DORIS:
                throw new UnsupportedOperationException("Doris parser not implemented yet");
            case SPARK_SQL:
                throw new UnsupportedOperationException("Spark SQL parser not implemented yet");
            default:
                throw new IllegalArgumentException("Unsupported SQL type: " + sqlType);
        }
    }
    
    /**
     * 解析FlinkSQL
     */
    private static LineageResult parseFlinkSql(String sql) {
        FlinkSqlLineageParser parser = new FlinkSqlLineageParser();
        FlinkSqlLineageParser.LineageResult result = parser.parseLineage(sql);
        
        return new LineageResult(
            result.getLineageGraph(),
            result.getTables(),
            result.getColumns()
        );
    }
    
    /**
     * 血缘解析结果
     */
    public static class LineageResult {
        private final MutableValueGraph<Object, String> lineageGraph;
        private final Map<String, CommonTable> tables;
        private final Map<String, CommonColumn> columns;
        
        public LineageResult(MutableValueGraph<Object, String> lineageGraph,
                           Map<String, CommonTable> tables,
                           Map<String, CommonColumn> columns) {
            this.lineageGraph = lineageGraph;
            this.tables = tables;
            this.columns = columns;
        }
        
        public MutableValueGraph<Object, String> getLineageGraph() {
            return lineageGraph;
        }
        
        public Map<String, CommonTable> getTables() {
            return tables;
        }
        
        public Map<String, CommonColumn> getColumns() {
            return columns;
        }
        
        /**
         * 获取所有表
         */
        public Collection<CommonTable> getAllTables() {
            return tables.values();
        }
        
        /**
         * 获取所有字段
         */
        public Collection<CommonColumn> getAllColumns() {
            return columns.values();
        }
        
        /**
         * 获取字段的上游依赖
         */
        public Set<Object> getUpstreamDependencies(CommonColumn column) {
            return lineageGraph.predecessors(column);
        }
        
        /**
         * 获取字段的下游依赖
         */
        public Set<Object> getDownstreamDependencies(CommonColumn column) {
            return lineageGraph.successors(column);
        }
        
        /**
         * 获取血缘关系的转换信息
         */
        public Optional<String> getTransform(Object source, Object target) {
            return lineageGraph.edgeValue(source, target);
        }
        
        /**
         * 转换为适合写入Neo4j的结构
         */
        public Neo4jLineageData toNeo4jData() {
            List<Neo4jNode> nodes = new ArrayList<>();
            List<Neo4jRelationship> relationships = new ArrayList<>();
            
            // 添加表节点
            for (CommonTable table : tables.values()) {
                Neo4jNode node = new Neo4jNode();
                node.setId(getTableId(table));
                node.setType("TABLE");
                node.setProperties(createTableProperties(table));
                nodes.add(node);
            }
            
            // 添加字段节点
            for (CommonColumn column : columns.values()) {
                Neo4jNode node = new Neo4jNode();
                node.setId(getColumnId(column));
                node.setType("COLUMN");
                node.setProperties(createColumnProperties(column));
                nodes.add(node);
            }
            
            // 添加关系
            for (Object node : lineageGraph.nodes()) {
                for (Object successor : lineageGraph.successors(node)) {
                    Neo4jRelationship relationship = new Neo4jRelationship();
                    relationship.setFromId(getNodeId(node));
                    relationship.setToId(getNodeId(successor));
                    relationship.setType("LINEAGE");
                    
                    Map<String, Object> properties = new HashMap<>();
                    lineageGraph.edgeValue(node, successor).ifPresent(transform -> 
                        properties.put("transform", transform));
                    relationship.setProperties(properties);
                    
                    relationships.add(relationship);
                }
            }
            
            return new Neo4jLineageData(nodes, relationships);
        }
        
        private String getTableId(CommonTable table) {
            return String.format("table_%s_%s_%s", 
                table.getCatalog() != null ? table.getCatalog() : "default",
                table.getSchema() != null ? table.getSchema() : "default",
                table.getName());
        }
        
        private String getColumnId(CommonColumn column) {
            return String.format("column_%s_%s_%s_%s",
                column.getCatalog() != null ? column.getCatalog() : "default",
                column.getSchema() != null ? column.getSchema() : "default",
                column.getTable(),
                column.getName());
        }
        
        private String getNodeId(Object node) {
            if (node instanceof CommonTable) {
                return getTableId((CommonTable) node);
            } else if (node instanceof CommonColumn) {
                return getColumnId((CommonColumn) node);
            }
            return node.toString();
        }
        
        private Map<String, Object> createTableProperties(CommonTable table) {
            Map<String, Object> properties = new HashMap<>();
            properties.put("catalog", table.getCatalog());
            properties.put("schema", table.getSchema());
            properties.put("name", table.getName());
            properties.put("comment", table.getComment());
            properties.put("originalSql", table.getOriginalSql());
            properties.put("transform", table.getTransform());
            return properties;
        }
        
        private Map<String, Object> createColumnProperties(CommonColumn column) {
            Map<String, Object> properties = new HashMap<>();
            properties.put("catalog", column.getCatalog());
            properties.put("schema", column.getSchema());
            properties.put("table", column.getTable());
            properties.put("name", column.getName());
            properties.put("columnType", column.getColumnType());
            properties.put("comment", column.getComment());
            properties.put("originalSql", column.getOriginalSql());
            properties.put("transform", column.getTransform());
            return properties;
        }
    }
    
    /**
     * Neo4j血缘数据结构
     */
    public static class Neo4jLineageData {
        private final List<Neo4jNode> nodes;
        private final List<Neo4jRelationship> relationships;
        
        public Neo4jLineageData(List<Neo4jNode> nodes, List<Neo4jRelationship> relationships) {
            this.nodes = nodes;
            this.relationships = relationships;
        }
        
        public List<Neo4jNode> getNodes() {
            return nodes;
        }
        
        public List<Neo4jRelationship> getRelationships() {
            return relationships;
        }
    }
    
    /**
     * Neo4j节点
     */
    public static class Neo4jNode {
        private String id;
        private String type;
        private Map<String, Object> properties;
        
        public String getId() {
            return id;
        }
        
        public void setId(String id) {
            this.id = id;
        }
        
        public String getType() {
            return type;
        }
        
        public void setType(String type) {
            this.type = type;
        }
        
        public Map<String, Object> getProperties() {
            return properties;
        }
        
        public void setProperties(Map<String, Object> properties) {
            this.properties = properties;
        }
    }
    
    /**
     * Neo4j关系
     */
    public static class Neo4jRelationship {
        private String fromId;
        private String toId;
        private String type;
        private Map<String, Object> properties;
        
        public String getFromId() {
            return fromId;
        }
        
        public void setFromId(String fromId) {
            this.fromId = fromId;
        }
        
        public String getToId() {
            return toId;
        }
        
        public void setToId(String toId) {
            this.toId = toId;
        }
        
        public String getType() {
            return type;
        }
        
        public void setType(String type) {
            this.type = type;
        }
        
        public Map<String, Object> getProperties() {
            return properties;
        }
        
        public void setProperties(Map<String, Object> properties) {
            this.properties = properties;
        }
    }
}
