package com.bdp.sqlflow.parser;

import com.bdp.sqlflow.parser.anltr4.FlinkSqlLexer;
import com.bdp.sqlflow.parser.anltr4.FlinkSqlParser;
import com.bdp.sqlflow.parser.stat.CommonColumn;
import com.bdp.sqlflow.parser.stat.CommonTable;
import com.google.common.graph.MutableValueGraph;
import com.google.common.graph.ValueGraphBuilder;
import org.antlr.v4.runtime.*;
import org.antlr.v4.runtime.tree.ParseTree;
import org.antlr.v4.runtime.tree.ParseTreeWalker;

import java.util.*;

/**
 * FlinkSQL血缘解析器
 * 基于ANTLR4实现FlinkSQL的字段级血缘解析
 * 
 * <AUTHOR>
 */
public class FlinkSqlLineageParser {
    
    private final MutableValueGraph<Object, String> lineageGraph;
    private final Map<String, CommonTable> tables;
    private final Map<String, CommonColumn> columns;
    
    public FlinkSqlLineageParser() {
        this.lineageGraph = ValueGraphBuilder.directed().build();
        this.tables = new HashMap<>();
        this.columns = new HashMap<>();
    }
    
    /**
     * 解析SQL并提取血缘关系
     * 
     * @param sql SQL语句
     * @return 血缘解析结果
     */
    public LineageResult parseLineage(String sql) {
        try {
            // 创建词法分析器
            CharStream input = CharStreams.fromString(sql);
            FlinkSqlLexer lexer = new FlinkSqlLexer(input);
            
            // 创建语法分析器
            CommonTokenStream tokens = new CommonTokenStream(lexer);
            FlinkSqlParser parser = new FlinkSqlParser(tokens);
            
            // 解析SQL
            ParseTree tree = parser.parse();
            
            // 创建访问者并遍历AST
            FlinkSqlLineageVisitor visitor = new FlinkSqlLineageVisitor(this);
            ParseTreeWalker walker = new ParseTreeWalker();
            walker.walk(visitor, tree);
            
            return new LineageResult(lineageGraph, tables, columns);
            
        } catch (Exception e) {
            throw new RuntimeException("Failed to parse SQL: " + sql, e);
        }
    }
    
    /**
     * 添加表到血缘图
     */
    public void addTable(CommonTable table) {
        String tableKey = getTableKey(table);
        tables.put(tableKey, table);
        lineageGraph.addNode(table);
    }
    
    /**
     * 添加字段到血缘图
     */
    public void addColumn(CommonColumn column) {
        String columnKey = getColumnKey(column);
        columns.put(columnKey, column);
        lineageGraph.addNode(column);
        
        // 建立字段与表的关系
        String tableKey = getTableKey(column.getCatalog(), column.getSchema(), column.getTable());
        CommonTable table = tables.get(tableKey);
        if (table != null) {
            lineageGraph.putEdgeValue(table, column, "CONTAINS");
        }
    }
    
    /**
     * 添加血缘关系
     */
    public void addLineage(Object source, Object target, String transform) {
        lineageGraph.putEdgeValue(source, target, transform != null ? transform : "DIRECT");
    }
    
    /**
     * 根据表名获取表对象
     */
    public CommonTable getTable(String catalog, String schema, String tableName) {
        String key = getTableKey(catalog, schema, tableName);
        return tables.get(key);
    }
    
    /**
     * 根据字段名获取字段对象
     */
    public CommonColumn getColumn(String catalog, String schema, String tableName, String columnName) {
        String key = getColumnKey(catalog, schema, tableName, columnName);
        return columns.get(key);
    }
    
    /**
     * 创建表对象
     */
    public CommonTable createTable(String catalog, String schema, String tableName) {
        CommonTable table = new CommonTable();
        table.setCatalog(catalog);
        table.setSchema(schema);
        table.setName(tableName);
        return table;
    }
    
    /**
     * 创建字段对象
     */
    public CommonColumn createColumn(String catalog, String schema, String tableName, String columnName, String columnType) {
        CommonColumn column = new CommonColumn();
        column.setCatalog(catalog);
        column.setSchema(schema);
        column.setTable(tableName);
        column.setName(columnName);
        column.setColumnType(columnType);
        return column;
    }
    
    /**
     * 获取表的唯一标识
     */
    private String getTableKey(CommonTable table) {
        return getTableKey(table.getCatalog(), table.getSchema(), table.getName());
    }
    
    private String getTableKey(String catalog, String schema, String tableName) {
        return String.format("%s.%s.%s", 
            catalog != null ? catalog : "", 
            schema != null ? schema : "", 
            tableName != null ? tableName : "");
    }
    
    /**
     * 获取字段的唯一标识
     */
    private String getColumnKey(CommonColumn column) {
        return getColumnKey(column.getCatalog(), column.getSchema(), column.getTable(), column.getName());
    }
    
    private String getColumnKey(String catalog, String schema, String tableName, String columnName) {
        return String.format("%s.%s.%s.%s", 
            catalog != null ? catalog : "", 
            schema != null ? schema : "", 
            tableName != null ? tableName : "",
            columnName != null ? columnName : "");
    }
    
    /**
     * 血缘解析结果
     */
    public static class LineageResult {
        private final MutableValueGraph<Object, String> lineageGraph;
        private final Map<String, CommonTable> tables;
        private final Map<String, CommonColumn> columns;
        
        public LineageResult(MutableValueGraph<Object, String> lineageGraph, 
                           Map<String, CommonTable> tables, 
                           Map<String, CommonColumn> columns) {
            this.lineageGraph = lineageGraph;
            this.tables = tables;
            this.columns = columns;
        }
        
        public MutableValueGraph<Object, String> getLineageGraph() {
            return lineageGraph;
        }
        
        public Map<String, CommonTable> getTables() {
            return tables;
        }
        
        public Map<String, CommonColumn> getColumns() {
            return columns;
        }
        
        /**
         * 获取所有表
         */
        public Collection<CommonTable> getAllTables() {
            return tables.values();
        }
        
        /**
         * 获取所有字段
         */
        public Collection<CommonColumn> getAllColumns() {
            return columns.values();
        }
        
        /**
         * 获取字段的上游依赖
         */
        public Set<Object> getUpstreamDependencies(CommonColumn column) {
            return lineageGraph.predecessors(column);
        }
        
        /**
         * 获取字段的下游依赖
         */
        public Set<Object> getDownstreamDependencies(CommonColumn column) {
            return lineageGraph.successors(column);
        }
        
        /**
         * 获取血缘关系的转换信息
         */
        public Optional<String> getTransform(Object source, Object target) {
            return lineageGraph.edgeValue(source, target);
        }
    }
}
