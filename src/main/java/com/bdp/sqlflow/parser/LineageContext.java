package com.bdp.sqlflow.parser;

import com.bdp.sqlflow.parser.stat.CommonTable;

import java.util.ArrayList;
import java.util.List;

/**
 * 血缘解析上下文
 * 用于在解析过程中保存状态信息
 * 
 * <AUTHOR>
 */
public class LineageContext {
    
    private CommonTable currentTable;
    private CommonTable targetTable;
    private List<CommonTable> sourceTables;
    private boolean inView;
    private LineageContext parentContext;
    
    public LineageContext() {
        this.sourceTables = new ArrayList<>();
        this.inView = false;
    }
    
    public LineageContext(LineageContext parentContext) {
        this();
        this.parentContext = parentContext;
    }
    
    public CommonTable getCurrentTable() {
        return currentTable;
    }
    
    public void setCurrentTable(CommonTable currentTable) {
        this.currentTable = currentTable;
    }
    
    public CommonTable getTargetTable() {
        return targetTable != null ? targetTable : 
               (parentContext != null ? parentContext.getTargetTable() : null);
    }
    
    public void setTargetTable(CommonTable targetTable) {
        this.targetTable = targetTable;
    }
    
    public List<CommonTable> getSourceTables() {
        List<CommonTable> allSourceTables = new ArrayList<>(sourceTables);
        if (parentContext != null) {
            allSourceTables.addAll(parentContext.getSourceTables());
        }
        return allSourceTables;
    }
    
    public void addSourceTable(CommonTable sourceTable) {
        this.sourceTables.add(sourceTable);
    }
    
    public boolean isInView() {
        return inView;
    }
    
    public void setInView(boolean inView) {
        this.inView = inView;
    }
    
    public LineageContext getParentContext() {
        return parentContext;
    }
    
    public void setParentContext(LineageContext parentContext) {
        this.parentContext = parentContext;
    }
}
