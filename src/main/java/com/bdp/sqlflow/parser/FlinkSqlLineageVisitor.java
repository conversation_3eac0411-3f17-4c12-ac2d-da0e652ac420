package com.bdp.sqlflow.parser;

import com.bdp.sqlflow.parser.anltr4.FlinkSqlParser;
import com.bdp.sqlflow.parser.anltr4.FlinkSqlParserBaseListener;
import com.bdp.sqlflow.parser.stat.CommonColumn;
import com.bdp.sqlflow.parser.stat.CommonTable;
import org.antlr.v4.runtime.tree.ParseTree;
import org.antlr.v4.runtime.tree.TerminalNode;

import java.util.*;

/**
 * FlinkSQL血缘解析访问者
 * 使用访问者模式遍历AST并提取血缘关系
 *
 * <AUTHOR>
 */
public class FlinkSqlLineageVisitor extends FlinkSqlParserBaseListener {

    private final FlinkSqlLineageParser parser;
    private final Stack<LineageContext> contextStack;
    private LineageContext currentContext;

    public FlinkSqlLineageVisitor(FlinkSqlLineageParser parser) {
        this.parser = parser;
        this.contextStack = new Stack<>();
        this.currentContext = new LineageContext();
    }

    @Override
    public void enterCreateTable(FlinkSqlParser.CreateTableContext ctx) {
        String tableName = getQualifiedName(ctx.qualifiedName());
        String[] parts = parseQualifiedName(tableName);

        CommonTable table = parser.createTable(parts[0], parts[1], parts[2]);
        parser.addTable(table);

        currentContext.setCurrentTable(table);
    }

    @Override
    public void enterColumnDefinition(FlinkSqlParser.ColumnDefinitionContext ctx) {
        if (currentContext.getCurrentTable() != null) {
            String columnName = ctx.identifier().getText();
            String columnType = getTypeText(ctx.type());

            CommonTable table = currentContext.getCurrentTable();
            CommonColumn column = parser.createColumn(
                    table.getCatalog(),
                    table.getSchema(),
                    table.getName(),
                    columnName,
                    columnType
            );

            parser.addColumn(column);
            table.getColumns().add(column);
        }
    }

    @Override
    public void enterCreateView(FlinkSqlParser.CreateViewContext ctx) {
        String viewName = getQualifiedName(ctx.qualifiedName());
        String[] parts = parseQualifiedName(viewName);

        CommonTable view = parser.createTable(parts[0], parts[1], parts[2]);
        parser.addTable(view);

        currentContext.setCurrentTable(view);
        currentContext.setInView(true);
    }

    @Override
    public void enterInsertInto(FlinkSqlParser.InsertIntoContext ctx) {
        String tableName = getQualifiedName(ctx.qualifiedName());
        String[] parts = parseQualifiedName(tableName);

        CommonTable targetTable = parser.getTable(parts[0], parts[1], parts[2]);
        if (targetTable == null) {
            targetTable = parser.createTable(parts[0], parts[1], parts[2]);
            parser.addTable(targetTable);
        }

        currentContext.setTargetTable(targetTable);
    }

    @Override
    public void enterQuerySpecification(FlinkSqlParser.QuerySpecificationContext ctx) {
        // 进入新的查询上下文
        contextStack.push(currentContext);
        currentContext = new LineageContext(currentContext);
    }

    @Override
    public void exitQuerySpecification(FlinkSqlParser.QuerySpecificationContext ctx) {
        // 退出查询上下文
        if (!contextStack.isEmpty()) {
            currentContext = contextStack.pop();
        }
    }

    @Override
    public void enterSelectSingle(FlinkSqlParser.SelectSingleContext ctx) {
        String alias = null;
        if (ctx.identifier() != null) {
            alias = ctx.identifier().getText();
        }

        // 解析表达式并建立血缘关系
        String expression = ctx.expression().getText();
        List<CommonColumn> sourceColumns = extractSourceColumns(ctx.expression());

        // 如果有目标表，建立血缘关系
        CommonTable targetTable = currentContext.getTargetTable();
        if (targetTable != null) {
            String targetColumnName = alias != null ? alias : extractColumnName(expression);
            CommonColumn targetColumn = parser.getColumn(
                    targetTable.getCatalog(),
                    targetTable.getSchema(),
                    targetTable.getName(),
                    targetColumnName
            );

            if (targetColumn == null) {
                targetColumn = parser.createColumn(
                        targetTable.getCatalog(),
                        targetTable.getSchema(),
                        targetTable.getName(),
                        targetColumnName,
                        "UNKNOWN"
                );
                parser.addColumn(targetColumn);
                targetTable.getColumns().add(targetColumn);
            }

            // 建立血缘关系
            for (CommonColumn sourceColumn : sourceColumns) {
                parser.addLineage(sourceColumn, targetColumn, expression);
            }
        }
    }

    @Override
    public void enterTableName(FlinkSqlParser.TableNameContext ctx) {
        String tableName = getQualifiedName(ctx.qualifiedName());
        String[] parts = parseQualifiedName(tableName);

        CommonTable table = parser.getTable(parts[0], parts[1], parts[2]);
        if (table == null) {
            table = parser.createTable(parts[0], parts[1], parts[2]);
            parser.addTable(table);
        }

        currentContext.addSourceTable(table);
    }

    @Override
    public void enterUnnest(FlinkSqlParser.UnnestContext ctx) {
        // 处理UNNEST语法
        String alias = null;
        if (ctx.identifier() != null) {
            // alias = ctx.identifier().getText();
            alias = "T";

        }

        // 解析UNNEST的表达式
        for (FlinkSqlParser.ExpressionContext expr : ctx.expression()) {
            List<CommonColumn> sourceColumns = extractSourceColumns(expr);

            // 为UNNEST创建虚拟表
            CommonTable unnestTable = parser.createTable(null, null, alias != null ? alias : "unnest_table");
            parser.addTable(unnestTable);

            // 处理UNNEST展开的字段
            if (ctx.identifier() != null && ctx.identifier().size() > 1) {
                for (int i = 1; i < ctx.identifier().size(); i++) {
                    String columnName = ctx.identifier(i).getText();
                    CommonColumn unnestColumn = parser.createColumn(
                            null, null, unnestTable.getName(), columnName, "UNKNOWN"
                    );
                    parser.addColumn(unnestColumn);
                    unnestTable.getColumns().add(unnestColumn);

                    // 建立血缘关系
                    for (CommonColumn sourceColumn : sourceColumns) {
                        parser.addLineage(sourceColumn, unnestColumn, "UNNEST");
                    }
                }
            }

            currentContext.addSourceTable(unnestTable);
        }
    }

    /**
     * 提取表达式中的源字段
     */
    private List<CommonColumn> extractSourceColumns(FlinkSqlParser.ExpressionContext ctx) {
        List<CommonColumn> columns = new ArrayList<>();

        // 递归遍历表达式树，提取字段引用
        extractColumnsFromExpression(ctx, columns);

        return columns;
    }

    /**
     * 从表达式中递归提取字段
     */
    private void extractColumnsFromExpression(ParseTree node, List<CommonColumn> columns) {
        if (node instanceof FlinkSqlParser.ColumnReferenceContext) {
            FlinkSqlParser.ColumnReferenceContext colRef = (FlinkSqlParser.ColumnReferenceContext) node;
            String columnName = colRef.identifier().getText();

            // 在当前上下文的源表中查找字段
            for (CommonTable sourceTable : currentContext.getSourceTables()) {
                CommonColumn column = parser.getColumn(
                        sourceTable.getCatalog(),
                        sourceTable.getSchema(),
                        sourceTable.getName(),
                        columnName
                );
                if (column != null) {
                    columns.add(column);
                    break;
                }
            }
        } else if (node instanceof FlinkSqlParser.DereferenceContext) {
            FlinkSqlParser.DereferenceContext deref = (FlinkSqlParser.DereferenceContext) node;
            String fieldName = deref.fieldName.getText();

            // 处理复杂字段引用，如 a.records.quality
            extractColumnsFromExpression(deref.base, columns);

            // 查找嵌套字段
            String baseExpression = deref.base.getText();
            for (CommonTable sourceTable : currentContext.getSourceTables()) {
                // 查找数组或复杂类型字段
                String nestedColumnName = baseExpression + "." + fieldName;
                CommonColumn column = findNestedColumn(sourceTable, nestedColumnName);
                if (column != null) {
                    columns.add(column);
                }
            }
        } else if (node instanceof FlinkSqlParser.SimpleCaseContext) {
            // 处理CASE WHEN表达式
            FlinkSqlParser.SimpleCaseContext caseExpr = (FlinkSqlParser.SimpleCaseContext) node;
            extractColumnsFromExpression(caseExpr.valueExpression(), columns);
            for (FlinkSqlParser.WhenClauseContext whenClause : caseExpr.whenClause()) {
                extractColumnsFromExpression(whenClause.condition, columns);
                extractColumnsFromExpression(whenClause.result, columns);
            }
            if (caseExpr.elseExpression != null) {
                extractColumnsFromExpression(caseExpr.elseExpression, columns);
            }
        } else if (node instanceof FlinkSqlParser.SearchedCaseContext) {
            // 处理搜索式CASE WHEN表达式
            FlinkSqlParser.SearchedCaseContext caseExpr = (FlinkSqlParser.SearchedCaseContext) node;
            for (FlinkSqlParser.WhenClauseContext whenClause : caseExpr.whenClause()) {
                extractColumnsFromExpression(whenClause.condition, columns);
                extractColumnsFromExpression(whenClause.result, columns);
            }
            if (caseExpr.elseExpression != null) {
                extractColumnsFromExpression(caseExpr.elseExpression, columns);
            }
        } else if (node instanceof FlinkSqlParser.FunctionCallContext) {
            // 处理函数调用
            FlinkSqlParser.FunctionCallContext funcCall = (FlinkSqlParser.FunctionCallContext) node;
            if (funcCall.expression() != null) {
                for (FlinkSqlParser.ExpressionContext expr : funcCall.expression()) {
                    extractColumnsFromExpression(expr, columns);
                }
            }
        }

        // 递归处理子节点
        for (int i = 0; i < node.getChildCount(); i++) {
            extractColumnsFromExpression(node.getChild(i), columns);
        }
    }

    /**
     * 查找嵌套字段（如数组、MAP中的字段）
     */
    private CommonColumn findNestedColumn(CommonTable table, String nestedColumnName) {
        // 处理复杂类型字段的展开
        String[] parts = nestedColumnName.split("\\.");

        // 查找基础字段
        String baseColumnName = parts[0];
        CommonColumn baseColumn = parser.getColumn(
                table.getCatalog(),
                table.getSchema(),
                table.getName(),
                baseColumnName
        );

        if (baseColumn != null && parts.length > 1) {
            // 处理嵌套字段，如records.quality
            String fullNestedName = nestedColumnName;
            CommonColumn nestedColumn = parser.getColumn(
                    table.getCatalog(),
                    table.getSchema(),
                    table.getName(),
                    fullNestedName
            );

            if (nestedColumn == null) {
                // 根据基础字段类型推断嵌套字段类型
                String nestedType = inferNestedColumnType(baseColumn.getColumnType(), parts);
                nestedColumn = parser.createColumn(
                        table.getCatalog(),
                        table.getSchema(),
                        table.getName(),
                        fullNestedName,
                        nestedType
                );
                parser.addColumn(nestedColumn);
                table.getColumns().add(nestedColumn);

                // 建立基础字段到嵌套字段的关系
                parser.addLineage(baseColumn, nestedColumn, "NESTED_ACCESS");
            }

            return nestedColumn;
        }

        return baseColumn;
    }

    /**
     * 推断嵌套字段类型
     */
    private String inferNestedColumnType(String baseType, String[] nestedParts) {
        if (baseType == null) return "UNKNOWN";

        // 处理ARRAY类型
        if (baseType.startsWith("ARRAY<")) {
            String innerType = baseType.substring(6, baseType.length() - 1);
            if (innerType.startsWith("ROW<")) {
                // 解析ROW类型中的字段
                return parseRowFieldType(innerType, nestedParts[1]);
            }
            return innerType;
        }

        // 处理MAP类型
        if (baseType.startsWith("MAP<")) {
            String[] mapTypes = baseType.substring(4, baseType.length() - 1).split(",", 2);
            return mapTypes.length > 1 ? mapTypes[1].trim() : "UNKNOWN";
        }

        // 处理ROW类型
        if (baseType.startsWith("ROW<")) {
            return parseRowFieldType(baseType, nestedParts[1]);
        }

        return "UNKNOWN";
    }

    /**
     * 解析ROW类型中的字段类型
     */
    private String parseRowFieldType(String rowType, String fieldName) {
        // 简化实现：解析ROW<field1 TYPE1, field2 TYPE2>格式
        String content = rowType.substring(4, rowType.length() - 1);
        String[] fields = content.split(",");

        for (String field : fields) {
            field = field.trim();
            String[] parts = field.split("\\s+", 2);
            if (parts.length == 2 && parts[0].equals(fieldName)) {
                return parts[1];
            }
        }

        return "UNKNOWN";
    }

    /**
     * 从表达式中提取字段名
     */
    private String extractColumnName(String expression) {
        // 简化实现：如果是简单的字段引用，直接返回
        if (expression.matches("[a-zA-Z_][a-zA-Z0-9_]*")) {
            return expression;
        }

        // 对于复杂表达式，返回表达式本身作为字段名
        return expression;
    }

    /**
     * 获取限定名称
     */
    private String getQualifiedName(FlinkSqlParser.QualifiedNameContext ctx) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < ctx.identifier().size(); i++) {
            if (i > 0) sb.append(".");
            sb.append(ctx.identifier(i).getText());
        }
        return sb.toString();
    }

    /**
     * 解析限定名称为catalog.schema.table
     */
    private String[] parseQualifiedName(String qualifiedName) {
        String[] parts = qualifiedName.split("\\.");
        String catalog = null;
        String schema = null;
        String table = null;

        if (parts.length == 1) {
            table = parts[0];
        } else if (parts.length == 2) {
            schema = parts[0];
            table = parts[1];
        } else if (parts.length >= 3) {
            catalog = parts[0];
            schema = parts[1];
            table = parts[2];
        }

        return new String[]{catalog, schema, table};
    }

    /**
     * 获取类型文本
     */
    private String getTypeText(FlinkSqlParser.TypeContext ctx) {
        return ctx.getText();
    }
}
