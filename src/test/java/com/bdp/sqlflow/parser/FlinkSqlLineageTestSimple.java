package com.bdp.sqlflow.parser;

import com.bdp.sqlflow.parser.stat.CommonColumn;
import com.bdp.sqlflow.parser.stat.CommonTable;
import org.testng.annotations.Test;
import org.testng.Assert;

import java.util.Collection;

/**
 * 简单的FlinkSQL血缘解析测试
 * 
 * <AUTHOR>
 */
public class FlinkSqlLineageTestSimple {
    
    @Test
    public void testSimpleCreateTable() {
        String sql = "CREATE TABLE test_table (" +
                "id INT," +
                "name STRING," +
                "age INT" +
                ") WITH (" +
                "'connector' = 'kafka'" +
                ");";
        
        try {
            SqlParserUtil.LineageResult result = SqlParserUtil.parseLineage(sql, SqlParserUtil.SqlType.FLINK_SQL);
            
            // 验证表的创建
            Collection<CommonTable> tables = result.getAllTables();
            Assert.assertFalse(tables.isEmpty(), "应该有表被创建");

            CommonTable table = tables.iterator().next();
            Assert.assertEquals(table.getName(), "test_table", "表名应该正确");

            // 验证字段的创建
            Collection<CommonColumn> columns = result.getAllColumns();
            Assert.assertEquals(columns.size(), 3, "应该有3个字段");

            boolean hasId = columns.stream().anyMatch(col -> "id".equals(col.getName()));
            boolean hasName = columns.stream().anyMatch(col -> "name".equals(col.getName()));
            boolean hasAge = columns.stream().anyMatch(col -> "age".equals(col.getName()));

            Assert.assertTrue(hasId, "应该有id字段");
            Assert.assertTrue(hasName, "应该有name字段");
            Assert.assertTrue(hasAge, "应该有age字段");
            
            System.out.println("测试通过：简单CREATE TABLE解析成功");
            
        } catch (Exception e) {
            e.printStackTrace();
            Assert.fail("解析失败: " + e.getMessage());
        }
    }
    
    @Test
    public void testBasicInsert() {
        String sql = "CREATE TABLE source_table (" +
                "id INT," +
                "name STRING" +
                ") WITH ('connector' = 'kafka');" +
                "CREATE TABLE target_table (" +
                "id INT," +
                "name STRING" +
                ") WITH ('connector' = 'kafka');" +
                "INSERT INTO target_table " +
                "SELECT id, name FROM source_table;";
        
        try {
            SqlParserUtil.LineageResult result = SqlParserUtil.parseLineage(sql, SqlParserUtil.SqlType.FLINK_SQL);
            
            // 验证表的创建
            Collection<CommonTable> tables = result.getAllTables();
            Assert.assertEquals(tables.size(), 2, "应该有2个表");

            // 验证字段的创建
            Collection<CommonColumn> columns = result.getAllColumns();
            Assert.assertTrue(columns.size() > 0, "应该有字段被创建");
            
            System.out.println("=== 表信息 ===");
            for (CommonTable table : tables) {
                System.out.println(String.format("表: %s", table.getName()));
            }
            
            System.out.println("\n=== 字段信息 ===");
            for (CommonColumn column : columns) {
                System.out.println(String.format("字段: %s.%s (%s)", 
                    column.getTable(), column.getName(), column.getColumnType()));
            }
            
            System.out.println("测试通过：基本INSERT解析成功");
            
        } catch (Exception e) {
            e.printStackTrace();
            Assert.fail("解析失败: " + e.getMessage());
        }
    }
}
