package io.github.melin.sqlflow.parser.flink

import io.github.melin.sqlflow.analyzer.Analysis
import io.github.melin.sqlflow.analyzer.StatementAnalyzer
import io.github.melin.sqlflow.parser.SqlFlowParser
import io.github.melin.sqlflow.util.JsonUtils
import org.junit.Test
import java.util.*

class FlinkSqlLineageTest {

    protected val SQL_PARSER = SqlFlowParser()

    @Test
    @Throws(Exception::class)
    fun testInsertInto() {
        val sql = """
          INSERT INTO paimon.DDH_PROD.dwd_mns_dgsj_real_data_di_new
            SELECT
                ods.p_day AS p_day,
                ods.`point` AS id_org,
                dim.pmis_code,
                ods.accept_time AS data_time,
                dim.point_name,
                ods.`value` origin_value,
                case when dim_map.pmis_code is null
                    then ods.`value` -- 没有关联，则无需做任何处理
                    else
                        case when dim_map.data_state is not null -- 状态映射
                                then IFNULL(cast(dim_map.data_state[cast(cast(ods.`value` as int) as STRING)] as DECIMAL(32,8)),ods.`value`)
                             when dim_map.data_tozero = 1
                                then
                                    case when ods.`value` < 0 then 0 -- <0 的转为0
                                         when dim_map.data_unit_coef = 1 then ods.`value` -- 若单位系数为1则说明无需转换单位
                                         else ods.`value`*dim_map.data_unit_coef
                                    end
                        end
                end as data_value,
                dim.org_code,
                dim.ppis_code,
                dim.site_code,
                dim.site_name,
                dim.main_system_code,
                dim.main_system_name,
                dim.sub_system_code,
                dim.sub_system_name,
                dim.unit_code,
                dim.unit_name,
                dim.components_code,
                dim.components_name,
                dim.monitor_code,
                dim.monitor_name,
                dim.ext_serialnum,
                dim.ext_source,
                dim.ext_mon_type,
                dim.ext_data_type,
                'ods_mns_dgsj_udp_data' AS data_source,
                ods.send_time AS push_time,
                cast(CURRENT_TIMESTAMP AS TIMESTAMP(0)) AS load_time
            FROM view_ods_mns_dgsj_udp_data ods
            INNER JOIN paimon.DDH_PROD.dim_mns_point /*+ OPTIONS('lookup.cache' = 'full')*/
            FOR SYSTEM_TIME AS OF ods.proc_time AS dim
                on ods.`point` = dim.id_org and dim.site_code = 'DGSJ' -- 吉牛编码
                and dim.start_time < ods.accept_time and dim.end_time > ods.accept_time
            LEFT JOIN view_dim_map_info FOR SYSTEM_TIME AS OF ods.proc_time as dim_map
            on dim.pmis_code=dim_map.pmis_code and dim_map.site_code='DGSJ'
            ;
        """.trimIndent()
        val statement = SQL_PARSER.createStatement(sql)
        val analysis = Analysis(statement, emptyMap())
        val statementAnalyzer = StatementAnalyzer(
            analysis,
            SimpleFlinkMetadataService(), SQL_PARSER
        )
        statementAnalyzer.analyze(statement, Optional.empty())

        System.out.println(JsonUtils.toJSONString(analysis.getTarget().get()));
    }

    @Test
    @Throws(Exception::class)
    fun testInsertInto1() {
        val sql = """
            INSERT INTO PROCESSED_MDM_PRODUCT_ENRICHMENT(PROD_ID, ENRICHMENT_ID)
            SELECT CASE
                WHEN TRIM(A.ITEM) = '' THEN CAST(NULL AS STRING)
                ELSE ITEM END AS PROD_ID, 
                ENRICHMENT_ID
            FROM RETEK_XX_ITEM_ATTR_TRANSLATE_PRODUCT_ENRICHMENT A
            LEFT JOIN MDM_DIM_XX_ITEM_ATTR_TRANSLATE_LOOKUPMAP_ORACLE_DIM B
            ON A.ITEM = B.PROD_ID
        """.trimIndent()
        val statement = SQL_PARSER.createStatement(sql)
        val analysis = Analysis(statement, emptyMap())
        val statementAnalyzer = StatementAnalyzer(
            analysis,
            SimpleFlinkMetadataService(), SQL_PARSER
        )
        statementAnalyzer.analyze(statement, Optional.empty())

        System.out.println(JsonUtils.toJSONString(analysis.getTarget().get()));
    }

    @Test
    @Throws(Exception::class)
    fun testInsertInto2() {
        val sql = """
            INSERT INTO PROCESSED_MDM_PRODUCT_ENRICHMENT(PROD_ID, ENRICHMENT_ID)
            SELECT ITEM AS PROD_ID, 
                ENRICHMENT_ID
            FROM RETEK_XX_ITEM_ATTR_TRANSLATE_PRODUCT_ENRICHMENT 
            union
            SELECT ITEM PROD_ID, 
                UDA_VALUE_ID ENRICHMENT_ID
            FROM MDM_DIM_XX_ITEM_ATTR_TRANSLATE_LOOKUPMAP_ORACLE_DIM 
        """.trimIndent()
        val statement = SQL_PARSER.createStatement(sql)
        val analysis = Analysis(statement, emptyMap())
        val statementAnalyzer = StatementAnalyzer(
            analysis,
            SimpleFlinkMetadataService(), SQL_PARSER
        )
        statementAnalyzer.analyze(statement, Optional.empty())

        System.out.println(JsonUtils.toJSONString(analysis.getTarget().get()));
    }

    @Test
    @Throws(Exception::class)
    fun testInsertInto3() {
        val sql = """
            INSERT INTO PROCESSED_MDM_PRODUCT_ENRICHMENT(PROD_ID, ENRICHMENT_ID)
            SELECT B.PROD_ID, A.ATTRIB_ID AS ENRICHMENT_ID FROM mdm_dim_product_attrib_type_lookupmap_mysql A
            LEFT JOIN (
                SELECT ITEM AS PROD_ID, 
                    ENRICHMENT_ID
                FROM RETEK_XX_ITEM_ATTR_TRANSLATE_PRODUCT_ENRICHMENT 
                union
                SELECT ITEM PROD_ID, 
                    UDA_VALUE_ID ENRICHMENT_ID
                FROM MDM_DIM_XX_ITEM_ATTR_TRANSLATE_LOOKUPMAP_ORACLE_DIM 
            ) B 
            ON A.PROD_ID = B.PROD_ID
        """.trimIndent()
        val statement = SQL_PARSER.createStatement(sql)
        val analysis = Analysis(statement, emptyMap())
        val statementAnalyzer = StatementAnalyzer(
            analysis,
            SimpleFlinkMetadataService(), SQL_PARSER
        )
        statementAnalyzer.analyze(statement, Optional.empty())

        System.out.println(JsonUtils.toJSONString(analysis.getTarget().get()));
    }

    @Test
    @Throws(Exception::class)
    fun testInsertInto4() {
        val sql = """
            INSERT IGNORE INTO PROCESSED_MDM_PRODUCT_ENRICHMENT(PROD_ID, ENRICHMENT_VALUE)
            SELECT ITEM AS PROD_ID, GROUP_CONCAT(LANG SEPARATOR '; ') AS ENRICHMENT_VALUE 
            FROM RETEK_XX_ITEM_ATTR_TRANSLATE_PRODUCT_ENRICHMENT
            GROUP BY ITEM
        """.trimIndent()
        val statement = SQL_PARSER.createStatement(sql)
        val analysis = Analysis(statement, emptyMap())
        val statementAnalyzer = StatementAnalyzer(
            analysis,
            SimpleFlinkMetadataService(), SQL_PARSER
        )
        statementAnalyzer.analyze(statement, Optional.empty())

        System.out.println(JsonUtils.toJSONString(analysis.getTarget().get()));
    }
}