package io.github.melin.sqlflow.parser.flink

import io.github.melin.sqlflow.analyzer.Analysis
import io.github.melin.sqlflow.analyzer.StatementAnalyzer
import io.github.melin.sqlflow.metadata.SchemaTable
import io.github.melin.sqlflow.metadata.SimpleMetadataService
import io.github.melin.sqlflow.metadata.ViewDefinition
import io.github.melin.sqlflow.parser.SqlFlowParser
import io.github.melin.sqlflow.util.JsonUtils
import io.github.melin.superior.common.relational.create.CommonTable
import io.github.melin.superior.common.relational.create.CreateTable
import io.github.melin.superior.common.relational.create.CreateView
import io.github.melin.superior.common.relational.dml.InsertTable
import io.github.melin.superior.common.relational.paimon.PaimonTable
import io.github.melin.superior.parser.flink.FlinkSqlHelper
import org.assertj.core.util.Lists
import org.junit.Test
import java.util.Optional

class ProdFlinksqlTest {

    protected val SQL_PARSER = SqlFlowParser()


    @Test
    @Throws(Exception::class)
    fun testEasy() {
        val sql = """
             CREATE TABLE kafka_MNS_DGSJ_UDP_DATA (
                `acceptTime` TIMESTAMP(3),
                `count` INT,
                `records` ARRAY<ROW<`point` INT, `quality` BOOLEAN, `time` TIMESTAMP(3), `value` DOUBLE>>,
                `sendTime` TIMESTAMP(3),
                `type` INT,
                `event_time` TIMESTAMP(3) METADATA FROM 'timestamp' 
            ) WITH (
                'connector' = 'kafka',
                'topic' = 'JN_EMS_UDP_DATA', 
                'scan.startup.mode' = 'earliest-offset',
                -- 'scan.startup.mode' = 'latest-offset',
                -- 'scan.startup.mode' = 'group-offsets',
                -- 'scan.startup.mode' = 'timestamp',
                -- 'scan.startup.timestamp-millis' = '1731816000000',
                'properties.bootstrap.servers'='************:19092,************:19092,************:19092,************:19092,************:19092',
                'properties.sasl.jaas.config'='org.apache.kafka.common.security.scram.ScramLoginModule required username="pbgdatauser" password="pbgdatauser@123";',
                'properties.sasl.mechanism'='SCRAM-SHA-256',
                'properties.security.protocol'='SASL_PLAINTEXT',
                'properties.group.id' = 'BIG_DATA_DATA_PLATFORM_2paimon002',
                'properties.client.id.prefix' = 'BIG_DATA_DATA_PLATFORM_2paimon',
                'properties.enable.auto.commit' = 'true',
                'properties.auto.offset.reset.strategy' = 'latest',
                'properties.auto.commit.interval.ms' = '60000',
                'properties.max.poll.records' = '100',
                'properties.max.partition.fetch.bytes' = '1024',
                'properties.session.timeout.ms' = '30000',
                'format' = 'json',
                'json.fail-on-missing-field' = 'false',
                'json.ignore-parse-errors' = 'true'
            );
            CREATE CATALOG paimon WITH (
              'type' = 'paimon',
              'warehouse' = 'hdfs://nameservice2/paimon/warehouse/'
            );
            """
        executeSql(sql)
    }

    @Test
    @Throws(Exception::class)
    fun testChildSelect() {
        val sql = """
            CREATE TABLE kafka_MNS_DGSJ_UDP_DATA (
                `acceptTime` TIMESTAMP(3),
                `count` INT,
                `records` ARRAY<ROW<`point` INT, `quality` BOOLEAN, `time` TIMESTAMP(3), `value` DOUBLE>>,
                `sendTime` TIMESTAMP(3),
                `type` INT,
                `event_time` TIMESTAMP(3) METADATA FROM 'timestamp' 
            ) WITH (
                'connector' = 'kafka',
                'topic' = 'JN_EMS_UDP_DATA', 
                'scan.startup.mode' = 'earliest-offset',
                -- 'scan.startup.mode' = 'latest-offset',
                -- 'scan.startup.mode' = 'group-offsets',
                -- 'scan.startup.mode' = 'timestamp',
                -- 'scan.startup.timestamp-millis' = '1731816000000',
                'properties.bootstrap.servers'='************:19092,************:19092,************:19092,************:19092,************:19092',
                'properties.sasl.jaas.config'='org.apache.kafka.common.security.scram.ScramLoginModule required username="pbgdatauser" password="pbgdatauser@123";',
                'properties.sasl.mechanism'='SCRAM-SHA-256',
                'properties.security.protocol'='SASL_PLAINTEXT',
                'properties.group.id' = 'BIG_DATA_DATA_PLATFORM_2paimon002',
                'properties.client.id.prefix' = 'BIG_DATA_DATA_PLATFORM_2paimon',
                'properties.enable.auto.commit' = 'true',
                'properties.auto.offset.reset.strategy' = 'latest',
                'properties.auto.commit.interval.ms' = '60000',
                'properties.max.poll.records' = '100',
                'properties.max.partition.fetch.bytes' = '1024',
                'properties.session.timeout.ms' = '30000',
                'format' = 'json',
                'json.fail-on-missing-field' = 'false',
                'json.ignore-parse-errors' = 'true'
            );
            CREATE CATALOG paimon WITH (
              'type' = 'paimon',
              'warehouse' = 'hdfs://nameservice2/paimon/warehouse/'
            );
            
            INSERT INTO paimon.DDH_PROD.dwd_mns_dgsj_real_data_di_new
            SELECT
                kmdud.records AS records,
                kmdud.accept_time AS data_time,
                ods.data_unit_coef AS data_unit_coef,
                ods.data_tozero AS data_tozero
                
                from kafka_MNS_DGSJ_UDP_DATA kmdud
                left join (
                
                    SELECT
                pmis_code
                ,site_code
                ,STR_TO_MAP(data_state,',',':') as data_state -- 状态转换
                ,data_unit_coef -- 单位系数
                ,data_tozero -- 判断是否需要置0
            FROM paimon.DDH_PROD.dim_mns_point_transform_mess
                ) ods on ods.pmis_code = kmdud.acceptTime
                ;
                
            CREATE TEMPORARY VIEW view_ods_mns_dgsj_udp_data
            AS
            SELECT
                date_format(`acceptTime`, 'yyyy-MM-dd') AS p_day,
                a.`acceptTime` AS accept_time,
                CASE WHEN a.`type` IN (0, 1) then concat('JN_A_', CAST(t.`point` AS STRING))
                    WHEN a.`type` IN (2, 3) then concat('JN_D_', CAST(t.`point` AS STRING))
                END AS `point`,
                a.`count`,
                t.`quality`,
                t.`time`,
                t.`value`,
                a.`sendTime` AS send_time,
                a.`type`,
                now() AS load_time,
                proctime() as proc_time
            FROM kafka_MNS_DGSJ_UDP_DATA a, UNNEST(`records`) AS t (`point`, `quality`, `time`, `value`)
            WHERE a.acceptTime is not null AND t.point is not null
            ;
        """
        executeSql(sql)

    }

    @Test
    @Throws(Exception::class)
    fun MNS_K2P_ODS_MNS_DGSJ_UDP_DATA2() {
        val sql = """
            
            -- **********************************************
            -- * 消费 kafka 吉牛监控数据写入 paimon ods/dwd 表
            -- * topic:      JN_EMS_UDP_DATA          
            -- * sink_table: paimon.DDH_PROD.ods_mns_dgsj_udp_data
            -- *             paimon.DDH_PROD.dwd_mns_dgsj_real_data_di
            -- *
            -- * topic：     JN_EMS_UDP_POINT
            -- * sink_table: paimon.DDH_PROD.ods_mns_dgsj_udp_point
            -- *
            -- * TODO: 由于吉牛的PMIS暂未编好，临时使用 dim_mns_point_without_pmis
            -- *       后续编码完成之后，直接将关联条件的维表切换成dim_mns_point即可
            -- ***********************************************

            ----------------------------------- 监控数据 -----------------------------------
            CREATE TABLE kafka_MNS_DGSJ_UDP_DATA (
                `acceptTime` TIMESTAMP(3),
                `count` INT,
                `records` ARRAY<ROW<`point` INT, `quality` BOOLEAN, `time` TIMESTAMP(3), `value` DOUBLE>>,
                `sendTime` TIMESTAMP(3),
                `type` INT,
                `event_time` TIMESTAMP(3) METADATA FROM 'timestamp' 
            ) WITH (
                'connector' = 'kafka',
                'topic' = 'JN_EMS_UDP_DATA', 
                'scan.startup.mode' = 'earliest-offset',
                -- 'scan.startup.mode' = 'latest-offset',
                -- 'scan.startup.mode' = 'group-offsets',
                -- 'scan.startup.mode' = 'timestamp',
                -- 'scan.startup.timestamp-millis' = '1731816000000',
                'properties.bootstrap.servers'='************:19092,************:19092,************:19092,************:19092,************:19092',
                'properties.sasl.jaas.config'='org.apache.kafka.common.security.scram.ScramLoginModule required username="pbgdatauser" password="pbgdatauser@123";',
                'properties.sasl.mechanism'='SCRAM-SHA-256',
                'properties.security.protocol'='SASL_PLAINTEXT',
                'properties.group.id' = 'BIG_DATA_DATA_PLATFORM_2paimon002',
                'properties.client.id.prefix' = 'BIG_DATA_DATA_PLATFORM_2paimon',
                'properties.enable.auto.commit' = 'true',
                'properties.auto.offset.reset.strategy' = 'latest',
                'properties.auto.commit.interval.ms' = '60000',
                'properties.max.poll.records' = '100',
                'properties.max.partition.fetch.bytes' = '1024',
                'properties.session.timeout.ms' = '30000',
                'format' = 'json',
                'json.fail-on-missing-field' = 'false',
                'json.ignore-parse-errors' = 'true'
            );

            -- 创建paimon的catalog连接
            CREATE CATALOG paimon WITH (
              'type' = 'paimon',
              'warehouse' = 'hdfs://nameservice2/paimon/warehouse/'
            );

            CREATE TEMPORARY VIEW view_ods_mns_dgsj_udp_data
            AS
            SELECT
                date_format(`acceptTime`, 'yyyy-MM-dd') AS p_day,
                a.`acceptTime` AS accept_time,
                CASE WHEN a.`type` IN (0, 1) then concat('JN_A_', CAST(t.`point` AS STRING))
                    WHEN a.`type` IN (2, 3) then concat('JN_D_', CAST(t.`point` AS STRING))
                END AS `point`,
                a.`count`,
                t.`quality`,
                t.`time`,
                t.`value`,
                a.`sendTime` AS send_time,
                a.`type`,
                now() AS load_time,
                proctime() as proc_time
            FROM kafka_MNS_DGSJ_UDP_DATA a, UNNEST(`records`) AS t (`point`, `quality`, `time`, `value`)
            WHERE a.acceptTime is not null AND t.point is not null
            ;

            CREATE TEMPORARY VIEW view_dim_map_info
            AS
            SELECT
                pmis_code
                ,site_code
                ,STR_TO_MAP(data_state,',',':') as data_state -- 状态转换
                ,data_unit_coef -- 单位系数
                ,data_tozero -- 判断是否需要置0
            FROM paimon.DDH_PROD.dim_mns_point_transform_mess -- 转换信息表
            ;

            -- 由于吉牛暂无PMIS编码，所以先使用临时维表，但是dwd明细层结构包含PMIS编码信息，
            -- 后续编码完成之后，直接将关联条件的维表切换成dim_mns_point即可
            INSERT INTO paimon.DDH_PROD.dwd_mns_dgsj_real_data_di_new
            SELECT
                ods.p_day AS p_day,
                ods.`point` AS id_org,
                ods.`quality`,
                ods.`time`,
                dim.pmis_code,
                ods.accept_time AS data_time,
                dim.point_name,
                ods.`value` origin_value,
                'ods_mns_dgsj_udp_data' AS data_source,
                case when dim_map.pmis_code is null
                    then ods.`value` -- 没有关联，则无需做任何处理
                    else
                        case when dim_map.data_state is not null -- 状态映射
                                then IFNULL(cast(dim_map.data_state[cast(cast(ods.`value` as int) as STRING)] as DECIMAL(32,8)),ods.`value`)
                             when dim_map.data_tozero = 1
                                then
                                    case when ods.`value` < 0 then 0 -- <0 的转为0
                                         when dim_map.data_unit_coef = 1 then ods.`value` -- 若单位系数为1则说明无需转换单位
                                         else ods.`value`*dim_map.data_unit_coef
                                    end
                        end
                end as data_value,
                dim.org_code,
                dim.ppis_code,
                dim.site_code,
                dim.site_name,
                dim.main_system_code,
                dim.main_system_name,
                dim.sub_system_code,
                dim.sub_system_name,
                dim.unit_code,
                dim.unit_name,
                dim.components_code,
                dim.components_name,
                dim.monitor_code,
                dim.monitor_name,
                dim.ext_serialnum,
                dim.ext_source,
                dim.ext_mon_type,
                dim.ext_data_type,
                ods.send_time AS push_time,
                cast(CURRENT_TIMESTAMP AS TIMESTAMP(0)) AS load_time
            FROM view_ods_mns_dgsj_udp_data ods
            INNER JOIN paimon.DDH_PROD.dim_mns_point /*+ OPTIONS('lookup.cache' = 'full')*/
            FOR SYSTEM_TIME AS OF ods.proc_time AS dim
                on ods.`point` = dim.id_org and dim.site_code = 'DGSJ' -- 吉牛编码
                and dim.start_time < ods.accept_time and dim.end_time > ods.accept_time
            LEFT JOIN view_dim_map_info FOR SYSTEM_TIME AS OF ods.proc_time as dim_map
            on dim.pmis_code=dim_map.pmis_code and dim_map.site_code='DGSJ'
            ;

        """.trimIndent()

        executeSql(sql)

    }


    private fun executeSql(sql: String) {
        // sqlflow 只支持 insert as select 语句血缘解析，create table语句需要先解析出元数据信息
        val metadataService = SimpleMetadataService("default")
        val statements = FlinkSqlHelper.parseMultiStatement(sql)
        val tables = Lists.newArrayList<SchemaTable>()
        for (statement in statements) {
            if (statement is CreateTable) {
                val columns = statement.columnRels?.map { it.columnName }
                val table = SchemaTable(
                    statement.tableId.catalogName,
                    statement.tableId.schemaName ?: "default",
                    statement.tableId.tableName,
                    columns
                )
                metadataService.addTableMetadata(table)
            } else if (statement is CreateView) {
                val columns = statement.columnRels?.map { it.columnName }
                val view = ViewDefinition(
                    statement.getSql(),
                    statement.tableId.catalogName,
                    statement.tableId.schemaName ?: "default",
                    statement.tableId.tableName,
                    columns,
                    null
                )
                metadataService.addViewDefinition(view)

            } else if (statement is PaimonTable) {
                val columns = statement.columnRels?.map { it.columnName }
                val paimonTable = SchemaTable(
                    statement.tableId.catalogName,
                    statement.tableId.schemaName ?: "paimon",
                    statement.tableId.tableName,
                    statement.alias,
                    columns
                )
                metadataService.addTableMetadata(paimonTable)

            } else if (statement is CommonTable) {
                val columns = statement.columnRels?.map { it.columnName }
                val schemaTable = SchemaTable(
                    statement.tableId.catalogName,
                    statement.tableId.schemaName ?: "default",
                    statement.tableId.tableName,
                    statement.alias,
                    columns
                )
                metadataService.addTableMetadata(schemaTable)

            } else if (statement is InsertTable) {
                val columns = statement.columnRels?.map { it.columnName }
                val table = SchemaTable(
                    statement.tableId.catalogName,
                    statement.tableId.schemaName ?: "default",
                    statement.tableId.tableName,
                    statement.alias,
                    columns
                )
                metadataService.addTableMetadata(table)

                if (statement.paimonTables.isNotEmpty()) {
                    statement.paimonTables.forEach {
                        val paimonTable = SchemaTable(
                            it.tableId.catalogName,
                            it.tableId.schemaName ?: "default",
                            it.tableId.tableName,
                            it.alias,
                            it.columnRels?.map { it.columnName }
                        )
                        metadataService.addTableMetadata(paimonTable)
                    }
                }
            }
        }
        metadataService.print()


        // 实例演示，设置设置表元数据，生产环境定制MetadataService，从元数据系统查询表字段信息。

        for (statement in statements) {
            if (statement is InsertTable) {
                val stat = SQL_PARSER.createStatement(statement.getSql())
                val analysis = Analysis(stat, emptyMap())
                val statementAnalyzer = StatementAnalyzer(
                    analysis,
                    metadataService,
                    SQL_PARSER
                )
                statementAnalyzer.analyze(stat, Optional.empty())

                println(JsonUtils.toJSONString(analysis.target.get()))
            }
        }

    }
    @Test
    @Throws(Exception::class)
    fun TEST_DGDG_INDEX() {
        """
            
            -- **********************************************
            -- * 读取 paimon 数据写入目标表                
            -- * source_talbe:  paimon.DDH_PROD.dwd_mns_dgdg_real_data_di
            -- ***********************************************

            -- 创建paimon的catalog连接
            CREATE CATALOG paimon WITH (
              'type' = 'paimon',
              'warehouse' = 'hdfs://nameservice2/paimon/warehouse/'
            );

            CREATE TABLE print_dm_mns_dgdg_index (
                window_start        TIMESTAMP(0),
                window_end          TIMESTAMP(0),
                data_time           TIMESTAMP(0),
                index_name          STRING,
                geiding_value       DOUBLE,
                fankui_value        DOUBLE,
                idx                 DOUBLE,
                data_source         STRING,
                load_time           TIMESTAMP(0)
            ) WITH (
                'connector' = 'print'
            );

            CREATE TEMPORARY VIEW view_dwd_mns_dgdg_real_data_di
            AS SELECT
                p_day,
                pmis_code,
                data_time,
                id_org,
                point_name,
                origin_value,
                data_value,
                org_code,
                ppis_code,
                site_code,
                site_name,
                main_system_code,
                main_system_name,
                sub_system_code,
                sub_system_name,
                unit_code,
                unit_name,
                components_code,
                components_name,
                monitor_code,
                monitor_name,
                ext_serialnum,
                ext_source,
                ext_mon_type,
                ext_data_type,
                data_source,
                push_time
            FROM paimon.DDH_PROD.dwd_mns_dgdg_real_data_di /*+ OPTIONS('consumer-id'='p2d_dgdg_index_calc','consumer.expiration-time'='3d','scan.parallelism'='1','scan.mode'='latest','scan.remove-normalize'='true') */ 
            ;

            INSERT INTO print_dm_mns_dgdg_index
            SELECT
                HOP_START(proctime(), INTERVAL '1' MINUTE, INTERVAL '5' MINUTE) window_start,
                HOP_END(proctime(), INTERVAL '1' MINUTE, INTERVAL '5' MINUTE) window_end,
                max(data_time) data_time,
                '开度偏差' index_name,
                max(case when pmis_code='*******.90.59' then data_value else 0 end) geiding_value,
                max(case when pmis_code='*******.84.59' then data_value else 0 end) fankui_value,
                (max(case when pmis_code='*******.90.59' then data_value else 0 end) - max(case when pmis_code='*******.84.59' then data_value else 0 end)) idx,
                'paimon.dwd_mns_dgdg_real_data_di' data_source,
                cast(NOW() as timestamp(0)) as load_time
            -- FROM (
            --     SELECT
            --         data_time,
            --         cast(data_time as timestamp(3)) as dt_time,
            --         pmis_code,
            --         data_value,
            --         proctime() as proc_time
            --     from view_dwd_mns_dgdg_real_data_di
            -- )
            from view_dwd_mns_dgdg_real_data_di
            -- /*+ OPTIONS('rowtime'='data_time', 'watermark'='ts - interval \'5\' second')*/
            where pmis_code in ('*******.84.59','*******.90.59')
            group by HOP(proctime(), INTERVAL '1' MINUTE, INTERVAL '5' MINUTE)
            ;


        """.trimIndent()

    }

    @Test
    @Throws(Exception::class)
    fun MNS_P2D_DM_ZIP_DATA() {
        """
            
            -- 创建paimon的catalog连接
            CREATE CATALOG paimon WITH (
              'type' = 'paimon',
              'warehouse' = 'hdfs://nameservice2:8020/paimon/warehouse/'
            );

            CREATE TABLE kafka_MNS_DPDP_UDP_DATA (
                `acceptTime` TIMESTAMP(3),
                `count` INT,
                -- `records` ARRAY<ROW<`point` INT, `quality` BOOLEAN, `time` TIMESTAMP(3), `value` DECIMAL(32,8)>>,
                `records` ARRAY<ROW<`point` STRING, `quality` BOOLEAN, `time` TIMESTAMP(3), `value` DECIMAL(32,8)>>,
                `sendTime` TIMESTAMP(3),
                `type` INT,
                WATERMARK FOR acceptTime as acceptTime
            ) WITH (
                'connector' = 'kafka',
                -- 'topic' = 'PBG_EMS_UDP_DATA',
                'topic' = 'PBG_EMS_UDP_DATA_NEW',
                -- 'scan.startup.mode' = 'earliest-offset',
                 'scan.startup.mode' = 'latest-offset',
            --    'scan.startup.mode' = 'group-offsets',
                -- 'scan.startup.mode' = 'timestamp',
                -- 'scan.startup.timestamp-millis' = '1752561960000',
                'properties.bootstrap.servers'='************:19092,************:19092,************:19092,************:19092,************:19092',
                'properties.sasl.jaas.config'='org.apache.kafka.common.security.scram.ScramLoginModule required username="pbgdatauser" password="pbgdatauser@123";',
                'properties.sasl.mechanism'='SCRAM-SHA-256',
                'properties.security.protocol'='SASL_PLAINTEXT',
                'properties.group.id' = 'MNS_PBG_EMS_UDP_DATA_NEW_2_MNS_P2D_DM_ZIP_DATA',
                'properties.client.id.prefix' = 'MNS_PBG_EMS_UDP_DATA_NEW_2_MNS_P2D_DM_ZIP_DATA',
                'properties.enable.auto.commit' = 'true',
                'properties.auto.offset.reset.strategy' = 'latest',
                'properties.auto.commit.interval.ms' = '60000',
                'properties.max.poll.records' = '1000',
                'properties.max.partition.fetch.bytes' = '10240',
                'properties.session.timeout.ms' = '30000',
                'format' = 'json',
                'json.fail-on-missing-field' = 'false',
                'json.ignore-parse-errors' = 'true'
            );


            create table doris_sink(
                data_time TIMESTAMP(0)
                ,pmis_code STRING
                ,ppis_code STRING
                ,point_name STRING
                ,data_value DOUBLE
                ,org_code STRING
                ,site_code STRING
                ,site_name STRING
                ,main_system_code STRING
                ,main_system_name STRING
                ,sub_system_code STRING
                ,sub_system_name STRING
                ,unit_code STRING
                ,unit_name STRING
                ,components_code STRING
                ,components_name STRING
                ,monitor_code STRING
                ,monitor_name STRING
                ,ext_serialnum STRING
                ,ext_source STRING
                ,ext_mon_type STRING
                ,ext_data_type STRING
                ,data_source STRING
                ,push_time TIMESTAMP(0)
                ,load_time TIMESTAMP(0)
            )with(
                'connector' = 'doris',
                'fenodes' = '*************:8031',
                'table.identifier' = 'DDH_PROD.dm_mns_d_point_5min_di',
                'username' = 'monitor',
                'password' = 'QAZwsx123!@#456',
                'sink.label-prefix' = 'dm_mns_d_point_5min_di_001'
            );


            CREATE TEMPORARY VIEW view_ods_mns_dpdp_udp_data
            AS
            SELECT
                date_format(`acceptTime`, 'yyyy-MM-dd') AS p_day,
                a.`acceptTime` AS accept_time,
                t.`point`,
                a.`count`,
                t.`quality`,
                t.`time`,
                t.`value`,
                a.`sendTime` AS send_time,
                a.`type`,
                now() AS load_time,
                proctime() as proc_time
            FROM kafka_MNS_DPDP_UDP_DATA a, UNNEST(`records`) AS t (`point`, `quality`, `time`, `value`)
            WHERE a.acceptTime is not null AND t.point is not null
            ;


            CREATE TEMPORARY VIEW view_dim_map_info
            AS
            SELECT
                pmis_code
                ,site_code
                ,STR_TO_MAP(data_state,',',':') as data_state -- 状态转换
                ,data_unit_coef -- 单位系数
                ,data_tozero -- 判断是否需要置0
            FROM paimon.DDH_PROD.dim_mns_point_transform_mess -- 转换信息表
            ;


            create TEMPORARY VIEW d_point_data AS
            SELECT
                ods.p_day AS p_day,
                ods.`point` AS id_org,
                dim.pmis_code,
                ods.accept_time AS data_time,
                dim.point_name,
                ods.`value` origin_value,
                case when dim_map.pmis_code is null
                    then ods.`value` -- 没有关联，则无需做任何处理
                    else
                        case when dim_map.data_state is not null -- 状态映射
                                then IFNULL(cast(dim_map.data_state[cast(cast(ods.`value` as int) as STRING)] as DECIMAL(32,8)),ods.`value`)
                             when dim_map.data_tozero = 1
                                then
                                    case when ods.`value` < 0 then 0 -- <0 的转为0
                                         when dim_map.data_unit_coef = 1 then ods.`value` -- 若单位系数为1则说明无需转换单位
                                         else ods.`value`*dim_map.data_unit_coef
                                    end
                        end
                end as data_value,
                dim.org_code,
                dim.ppis_code,
                dim.site_code,
                dim.site_name,
                dim.main_system_code,
                dim.main_system_name,
                dim.sub_system_code,
                dim.sub_system_name,
                dim.unit_code,
                dim.unit_name,
                dim.components_code,
                dim.components_name,
                dim.monitor_code,
                dim.monitor_name,
                dim.ext_serialnum,
                dim.ext_source,
                dim.ext_mon_type,
                dim.ext_data_type,
                'ods_mns_dpdp_udp_data' AS data_source,
                ods.send_time AS push_time,
                cast(CURRENT_TIMESTAMP AS TIMESTAMP(0)) AS load_time
            FROM view_ods_mns_dpdp_udp_data ods
            INNER JOIN paimon.DDH_PROD.dim_mns_point /*+ OPTIONS('lookup.cache' = 'full')*/
            FOR SYSTEM_TIME AS OF ods.proc_time AS dim
                on ods.`point` = dim.logic_code and dim.site_code = 'DPDP' -- 瀑布沟编码
                and dim.start_time < ods.accept_time and dim.end_time > ods.accept_time
            LEFT JOIN view_dim_map_info FOR SYSTEM_TIME AS OF ods.proc_time as dim_map
                on dim.pmis_code=dim_map.pmis_code and dim_map.site_code='DPDP'
            where dim.ext_mon_type = 'D'
            ;



            insert into doris_sink
            select
                data_time
                ,pmis_code
                ,ppis_code
                ,point_name
                ,data_value
                ,org_code
                ,site_code
                ,site_name
                ,main_system_code
                ,main_system_name
                ,sub_system_code
                ,sub_system_name
                ,unit_code
                ,unit_name
                ,components_code
                ,components_name
                ,monitor_code
                ,monitor_name
                ,ext_serialnum
                ,ext_source
                ,ext_mon_type
                ,ext_data_type
                ,data_source
                ,push_time
                ,now() as load_time
            from (
                select
                    data_time
                    ,pmis_code
                    ,ppis_code
                    ,point_name
                    ,data_value
                    ,org_code
                    ,site_code
                    ,site_name
                    ,main_system_code
                    ,main_system_name
                    ,sub_system_code
                    ,sub_system_name
                    ,unit_code
                    ,unit_name
                    ,components_code
                    ,components_name
                    ,monitor_code
                    ,monitor_name
                    ,ext_serialnum
                    ,ext_source
                    ,ext_mon_type
                    ,ext_data_type
                    ,data_source
                    ,push_time
                    ,now() as load_time
                    ,lag(cast(data_value as DOUBLE),1,CAST(100.0 as DOUBLE )) over(partition by pmis_code,window_start,window_end order by data_time) as pre_value
                from TABLE(TUMBLE(TABLE d_point_data,DESCRIPTOR(data_time),INTERVAL '5' MINUTE))
            ) a
            where cast(a.data_value as DOUBLE) <> CAST(a.pre_value as DOUBLE)
            ;

        """.trimIndent()

    }

    @Test
    @Throws(Exception::class)
    fun MEM_P2D_DM_EQUIP_RUN_DATA_DPDP() {
        """
            
            -- 创建paimon的catalog连接
            CREATE CATALOG paimon WITH (
              'type' = 'paimon',
              'warehouse' = 'hdfs://nameservice2:8020/paimon/warehouse/'
            );


            create table doris_dm_mns_equip_run_data_di(
                p_day STRING
                ,pmis_code STRING
                ,site_code STRING
                ,dict_code STRING
                ,water_level_lower int
                ,point_name STRING
                ,site_name STRING
                ,main_system_code STRING
                ,main_system_name STRING
                ,sub_system_code STRING
                ,sub_system_name STRING
                ,unit_code STRING
                ,unit_name STRING
                ,components_code STRING
                ,components_name STRING
                ,monitor_code STRING
                ,monitor_name STRING
                ,ext_serialnum STRING
                ,ext_source STRING
                ,ext_mon_type STRING
                ,ext_data_type STRING
                ,dict_name STRING
                ,max_value DECIMAL(32,4)
                ,min_value DECIMAL(32,4)
                ,sum_value DECIMAL(32,4)
                ,sum_count INT
                ,latest_value DECIMAL(32,4)
                ,latest_time TIMESTAMP(0)
                ,load_time TIMESTAMP(0)
                ,source_code STRING
            )with(
             	'connector' = 'doris',
             	'fenodes' = '*************:8031',
             	'table.identifier' = 'DDH_PROD.dm_mns_equip_run_data_di',
             	'username' = 'monitor',
             	'password' = 'QAZwsx123!@#456',
             	'sink.label-prefix' = 'MEM_P2D_DM_EQUIP_RUN_DATA_DPDP_001'
            );




            insert into doris_dm_mns_equip_run_data_di
            select
                p_day
                ,pmis_code
                ,site_code
                ,'Unconditional' as dict_code
                ,cast(null as int) as water_level_lower
                ,point_name
                ,site_name
                ,main_system_code
                ,main_system_name
                ,sub_system_code
                ,sub_system_name
                ,unit_code
                ,unit_name
                ,components_code
                ,components_name
                ,monitor_code
                ,monitor_name
                ,ext_serialnum
                ,ext_source
                ,ext_mon_type
                ,ext_data_type
                ,'无条件' AS dict_name
                ,data_value AS max_value
                ,data_value AS min_value
                ,data_value AS sum_value
                ,1 AS sum_count
                ,data_value AS latest_value
                ,data_time as latest_time
                ,NOW() AS load_time
                ,'paimon.dwd_mem_dpdp_real_data_di' AS source_code
            from paimon.DDH_PROD.dwd_mem_dpdp_real_data_di /*+ OPTIONS('consumer-id' = 'MEM_P2D_DM_EQUIP_RUN_DATA_DI','consumer.expiration-time'='3d','scan.mode'='latest' ,'scan.parallelism'='1') */
            where ext_mon_type <> 'D'
                and cast(data_time as TIMESTAMP(3)) <= cast(now() as TIMESTAMP(3))
            ;

        """.trimIndent()

    }
}