package com.bdp.sqlflow.parser

import com.bdp.sqlflow.parser.stat.CommonColumn
import com.bdp.sqlflow.parser.stat.CommonTable
import org.junit.Assert
import org.junit.Test

class FlinkSqlTest {

    @Test
    fun testComplexFlinkSqlLineage() {
        val sql = """
            CREATE TABLE kafka_MNS_DGSJ_UDP_DATA (
                `acceptTime` TIMESTAMP(3),
                `count` INT,
                `records` ARRAY<ROW<`point` INT, `quality` BOOLEAN, `time` TIMESTAMP(3), `value` DOUBLE>>,
                `sendTime` TIMESTAMP(3),
                `type` INT,
                `event_time` TIMESTAMP(3) METADATA FROM 'timestamp' 
            ) WITH (
                'connector' = 'kafka',
                'topic' = 'JN_EMS_UDP_DATA', 
                'scan.startup.mode' = 'earliest-offset',
                'properties.bootstrap.servers'='10.163.26.95:19092,10.163.26.96:19092,10.163.26.97:19092,10.163.26.98:19092,10.163.26.99:19092',
                'properties.sasl.jaas.config'='org.apache.kafka.common.security.scram.ScramLoginModule required username="pbgdatauser" password="pbgdatauser@123";',
                'properties.sasl.mechanism'='SCRAM-SHA-256',
                'properties.security.protocol'='SASL_PLAINTEXT',
                'properties.group.id' = 'BIG_DATA_DATA_PLATFORM_2paimon002',
                'properties.client.id.prefix' = 'BIG_DATA_DATA_PLATFORM_2paimon',
                'properties.enable.auto.commit' = 'true',
                'properties.auto.offset.reset.strategy' = 'latest',
                'properties.auto.commit.interval.ms' = '60000',
                'properties.max.poll.records' = '100',
                'properties.max.partition.fetch.bytes' = '1024',
                'properties.session.timeout.ms' = '30000',
                'format' = 'json',
                'json.fail-on-missing-field' = 'false',
                'json.ignore-parse-errors' = 'true'
            );

            CREATE CATALOG paimon WITH (
              'type' = 'paimon',
              'warehouse' = 'hdfs://nameservice2/paimon/warehouse/'
            );

            CREATE TEMPORARY VIEW view_ods_mns_dgsj_udp_data
            AS
            SELECT
                date_format(`acceptTime`, 'yyyy-MM-dd') AS p_day,
                a.`acceptTime` AS accept_time,
                CASE WHEN a.`type` IN (0, 1) then concat('JN_A_', CAST(t.`point` AS STRING))
                    WHEN a.`type` IN (2, 3) then concat('JN_D_', CAST(t.`point` AS STRING))
                END AS `point`,
                a.`count`,
                t.`quality`,
                t.`time`,
                t.`value`,
                a.`sendTime` AS send_time,
                a.`type`,
                now() AS load_time,
                proctime() as proc_time
            FROM kafka_MNS_DGSJ_UDP_DATA a, UNNEST(`records`) AS t (`point`, `quality`, `time`, `value`)
            WHERE a.acceptTime is not null AND t.point is not null
            ;

            CREATE TEMPORARY VIEW view_dim_map_info
            AS
            SELECT
                pmis_code
                ,site_code
                ,STR_TO_MAP(data_state,',',':') as data_state
                ,data_unit_coef
                ,data_tozero
            FROM paimon.DDH_PROD.dim_mns_point_transform_mess
            ;

            INSERT INTO paimon.DDH_PROD.dwd_mns_dgsj_real_data_di_new
            SELECT
                ods.p_day AS p_day,
                ods.`point` AS id_org,
                ods.`quality`,
                ods.`time`,
                dim.pmis_code,
                ods.accept_time AS data_time,
                dim.point_name,
                ods.`value` origin_value,
                'ods_mns_dgsj_udp_data' AS data_source,
                case when dim_map.pmis_code is null
                    then ods.`value`
                    else
                        case when dim_map.data_state is not null
                                then IFNULL(cast(dim_map.data_state[cast(cast(ods.`value` as int) as STRING)] as DECIMAL(32,8)),ods.`value`)
                             when dim_map.data_tozero = 1
                                then
                                    case when ods.`value` < 0 then 0
                                         when dim_map.data_unit_coef = 1 then ods.`value`
                                         else ods.`value`*dim_map.data_unit_coef
                                    end
                        end
                end as data_value,
                dim.org_code,
                dim.ppis_code,
                dim.site_code,
                dim.site_name,
                dim.main_system_code,
                dim.main_system_name,
                dim.sub_system_code,
                dim.sub_system_name,
                dim.unit_code,
                dim.unit_name,
                dim.components_code,
                dim.components_name,
                dim.monitor_code,
                dim.monitor_name,
                dim.ext_serialnum,
                dim.ext_source,
                dim.ext_mon_type,
                dim.ext_data_type,
                ods.send_time AS push_time,
                cast(CURRENT_TIMESTAMP AS TIMESTAMP(0)) AS load_time
            FROM view_ods_mns_dgsj_udp_data ods
            INNER JOIN paimon.DDH_PROD.dim_mns_point
            FOR SYSTEM_TIME AS OF ods.proc_time AS dim
                on ods.`point` = dim.id_org and dim.site_code = 'DGSJ'
                and dim.start_time < ods.accept_time and dim.end_time > ods.accept_time
            LEFT JOIN view_dim_map_info FOR SYSTEM_TIME AS OF ods.proc_time as dim_map
            on dim.pmis_code=dim_map.pmis_code and dim_map.site_code='DGSJ'
            ;
            
            """.trimIndent()


        // 解析SQL
        val result = SqlParserUtil.parseLineage(sql, SqlParserUtil.SqlType.FLINK_SQL)


        // 验证表的创建
        val tables = result.getAllTables()
        Assert.assertFalse("应该有表被创建", tables.isEmpty())


        // 验证kafka表的创建
        val foundKafkaTable = tables.stream()
            .anyMatch { table: CommonTable? -> "kafka_MNS_DGSJ_UDP_DATA" == table!!.getName() }
        Assert.assertTrue("应该找到kafka表", foundKafkaTable)


        // 验证字段的创建
        val columns = result.getAllColumns()
        Assert.assertFalse("应该有字段被创建", columns.isEmpty())


        // 验证复杂字段的展开（如records.quality）
        val foundNestedColumn = columns.stream()
            .anyMatch { column: CommonColumn? -> column!!.getName().contains("records.quality") }
        Assert.assertTrue("应该找到嵌套字段records.quality", foundNestedColumn)


        // 验证血缘关系
        val targetQualityColumn = columns.stream()
            .filter { column: CommonColumn? ->
                "dwd_mns_dgsj_real_data_di_new" == column!!.getTable()
                        && "quality" == column.getName()
            }
            .findFirst()
            .orElse(null)

        if (targetQualityColumn != null) {
            // 验证上游依赖
            Assert.assertFalse(
                "目标字段应该有上游依赖",
                result.getUpstreamDependencies(targetQualityColumn).isEmpty()
            )
        }


        // 输出结果用于调试
        println("=== 表信息 ===")
        for (table in tables) {
            println(
                String.format(
                    "表: %s.%s.%s",
                    table.getCatalog(), table.getSchema(), table.getName()
                )
            )
        }

        println("\n=== 字段信息 ===")
        for (column in columns) {
            println(
                String.format(
                    "字段: %s.%s.%s.%s (%s)",
                    column.getCatalog(), column.getSchema(), column.getTable(),
                    column.getName(), column.getColumnType()
                )
            )
        }

        println("\n=== 血缘关系 ===")
        for (column in columns) {
            if (!result.getUpstreamDependencies(column).isEmpty()) {
                println(
                    String.format(
                        "字段 %s.%s 的上游依赖:",
                        column.getTable(), column.getName()
                    )
                )
                for (upstream in result.getUpstreamDependencies(column)) {
                    if (upstream is CommonColumn) {
                        val upstreamCol = upstream
                        val transform = result.getTransform(upstream, column).orElse("DIRECT")
                        println(
                            String.format(
                                "  <- %s.%s (%s)",
                                upstreamCol.getTable(), upstreamCol.getName(), transform
                            )
                        )
                    }
                }
            }
        }


        // 转换为Neo4j格式
        val neo4jData = result.toNeo4jData()
        Assert.assertFalse("Neo4j节点不应为空", neo4jData.getNodes().isEmpty())
        Assert.assertFalse("Neo4j关系不应为空", neo4jData.getRelationships().isEmpty())

        println("\n=== Neo4j数据 ===")
        println("节点数量: " + neo4jData.getNodes().size)
        println("关系数量: " + neo4jData.getRelationships().size)
    }

    @Test
    fun testSimpleCreateTable() {
        val sql = """
            CREATE TABLE test_table (
                id INT,
                name STRING,
                age INT
            ) WITH (
                'connector' = 'kafka'
            );
            
            """.trimIndent()

        val result = SqlParserUtil.parseLineage(sql, SqlParserUtil.SqlType.FLINK_SQL)


        // 验证表的创建
        val tables = result.getAllTables()
        Assert.assertEquals("应该有1个表", 1, tables.size.toLong())

        val table = tables.iterator().next()
        Assert.assertEquals("表名应该正确", "test_table", table.getName())


        // 验证字段的创建
        val columns = result.getAllColumns()
        Assert.assertEquals("应该有3个字段", 3, columns.size.toLong())

        val hasId = columns.stream().anyMatch { col: CommonColumn? -> "id" == col!!.getName() }
        val hasName = columns.stream().anyMatch { col: CommonColumn? -> "name" == col!!.getName() }
        val hasAge = columns.stream().anyMatch { col: CommonColumn? -> "age" == col!!.getName() }

        Assert.assertTrue("应该有id字段", hasId)
        Assert.assertTrue("应该有name字段", hasName)
        Assert.assertTrue("应该有age字段", hasAge)
    }
}